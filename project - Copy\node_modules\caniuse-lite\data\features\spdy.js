module.exports={A:{A:{"1":"B","2":"K D E F A eC"},B:{"2":"4 5 6 7 8 9 C L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB I"},C:{"1":"0 1 2 3 L M G N O P JB y z KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB","2":"4 5 6 7 8 9 fC GC J IB K D E F A B C jB kB lB mB nB oB pB qB HC rB IC sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B Q H R JC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB I 9B KC LC gC hC iC"},D:{"1":"0 1 2 3 J IB K D E F A B C L M G N O P JB y z KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB","2":"4 5 6 7 8 9 jB kB lB mB nB oB pB qB HC rB IC sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB I 9B KC LC"},E:{"1":"E F A B C nC NC AC","2":"J IB K D jC MC kC lC mC","129":"L M G BC oC pC qC OC PC CC rC DC QC RC SC TC UC sC EC VC WC XC YC ZC aC FC bC tC"},F:{"1":"0 1 2 3 G N O P JB y z KB LB MB NB OB PB QB RB SB TB UB VB WB XB aB cB BC","2":"F B C YB ZB bB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B Q H R JC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x uC vC wC xC AC cC yC"},G:{"1":"E 3C 4C 5C 6C 7C 8C 9C AD","2":"MC zC dC 0C 1C 2C","257":"BD CD DD ED FD GD HD ID OC PC CC JD DC QC RC SC TC UC KD EC VC WC XC YC ZC aC FC bC"},H:{"2":"LD"},I:{"1":"GC J PD dC QD RD","2":"I MD ND OD"},J:{"2":"D A"},K:{"1":"BC","2":"A B C H AC cC"},L:{"2":"I"},M:{"2":"9B"},N:{"1":"B","2":"A"},O:{"2":"CC"},P:{"1":"J","2":"0 1 2 3 y z SD TD UD VD WD NC XD YD ZD aD bD DC EC FC cD"},Q:{"2":"dD"},R:{"2":"eD"},S:{"1":"fD","2":"gD"}},B:7,C:"SPDY protocol",D:true};
