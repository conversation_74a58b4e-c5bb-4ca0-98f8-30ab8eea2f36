import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User } from '../types';
import { generateId } from '../lib/utils';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => void;
  register: (name: string, email: string, password: string) => Promise<void>;
  updateProfile: (updates: Partial<Pick<User, 'name' | 'avatar'>>) => Promise<void>;
  clearUserData: () => void;
}

// Simple user database simulation using localStorage
const getUserDatabase = () => {
  const stored = localStorage.getItem('docuchat-users');
  return stored ? JSON.parse(stored) : {};
};

const saveUserDatabase = (users: Record<string, User & { password: string }>) => {
  localStorage.setItem('docuchat-users', JSON.stringify(users));
};

// Enhanced authentication with real user management
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email, password) => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const users = getUserDatabase();
          const userKey = email.toLowerCase();
          const storedUser = users[userKey];

          if (!storedUser || storedUser.password !== password) {
            throw new Error('Invalid email or password');
          }

          const user: User = {
            id: storedUser.id,
            name: storedUser.name,
            email: storedUser.email,
            role: storedUser.role,
            avatar: storedUser.avatar,
          };

          set({ user, isAuthenticated: true });
        } catch (error) {
          console.error('Login failed:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      loginWithGoogle: async () => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const googleEmail = '<EMAIL>';
          const users = getUserDatabase();
          const userKey = googleEmail.toLowerCase();

          let user: User;
          if (users[userKey]) {
            // Existing Google user
            const storedUser = users[userKey];
            user = {
              id: storedUser.id,
              name: storedUser.name,
              email: storedUser.email,
              role: storedUser.role,
              avatar: storedUser.avatar,
            };
          } else {
            // New Google user
            const userId = generateId();
            user = {
              id: userId,
              name: 'Google User',
              email: googleEmail,
              role: 'user',
              avatar: 'https://i.pravatar.cc/300',
            };

            users[userKey] = { ...user, password: 'google-auth' };
            saveUserDatabase(users);
          }

          set({ user, isAuthenticated: true });
        } catch (error) {
          console.error('Google login failed:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      logout: () => {
        // Clear user data from other stores
        get().clearUserData();
        set({ user: null, isAuthenticated: false });
      },

      register: async (name, email, password) => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const users = getUserDatabase();
          const userKey = email.toLowerCase();

          if (users[userKey]) {
            throw new Error('User with this email already exists');
          }

          const userId = generateId();
          const user: User = {
            id: userId,
            name,
            email,
            role: 'user',
            avatar: `https://i.pravatar.cc/300?u=${userId}`,
          };

          users[userKey] = { ...user, password };
          saveUserDatabase(users);

          set({ user, isAuthenticated: true });
        } catch (error) {
          console.error('Registration failed:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      updateProfile: async (updates) => {
        const { user } = get();
        if (!user) {
          throw new Error('No user logged in');
        }

        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 500));

          const users = getUserDatabase();
          const userKey = user.email.toLowerCase();
          const storedUser = users[userKey];

          if (!storedUser) {
            throw new Error('User not found');
          }

          // Update user data
          const updatedUser = { ...user, ...updates };
          const updatedStoredUser = { ...storedUser, ...updates };

          // Save to database
          users[userKey] = updatedStoredUser;
          saveUserDatabase(users);

          // Update state
          set({ user: updatedUser });
        } catch (error) {
          console.error('Profile update failed:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      clearUserData: () => {
        // Clear data from other stores
        if (typeof window !== 'undefined') {
          // Clear bot store data
          localStorage.removeItem('docuchat-bots');
          // Clear chat store data
          localStorage.removeItem('docuchat-chats');
        }
      },
    }),
    {
      name: 'docuchat-auth',
      storage: createJSONStorage(() => localStorage),
    }
  )
);