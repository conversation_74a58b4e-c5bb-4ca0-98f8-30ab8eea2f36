import React from 'react';
import { Link } from 'react-router-dom';
import { 
  FileText, 
  Search, 
  MessageCircle, 
  Brain, 
  Target, 
  Bookmark, 
  Globe, 
  Mic, 
  Shield, 
  Download,
  ArrowRight
} from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '../components/layout/Navbar';

const FeaturesPage: React.FC = () => {
  const features = [
    {
      icon: FileText,
      title: "📄 Multi-PDF Upload Support",
      description: "Let users upload and chat with multiple documents at once.",
      color: "text-pink-400"
    },
    {
      icon: Search,
      title: "🔍 Smart Document Search",
      description: "Add a search bar to instantly find keywords or phrases within the document.",
      color: "text-blue-400"
    },
    {
      icon: MessageCircle,
      title: "💬 Context-Aware Chat History",
      description: "Keep a thread of the conversation so the chatbot remembers context across multiple questions.",
      color: "text-green-400"
    },
    {
      icon: Brain,
      title: "🧠 Summarization Mode",
      description: "One-click summary of the whole document or selected sections.",
      color: "text-purple-400"
    },
    {
      icon: Target,
      title: "🎯 Ask Specific Sections",
      description: "Let users select or highlight parts of the document and ask questions directly about them.",
      color: "text-yellow-400"
    },
    {
      icon: Bookmark,
      title: "📌 Save Important Answers",
      description: "Allow users to \"pin\" or bookmark answers for future reference.",
      color: "text-orange-400"
    },
    {
      icon: Globe,
      title: "🌐 Language Translation",
      description: "Enable support to translate document content and chatbot replies into multiple languages.",
      color: "text-cyan-400"
    },
    {
      icon: Mic,
      title: "🎤 Voice Input",
      description: "Let users ask questions by speaking instead of typing.",
      color: "text-red-400"
    },
    {
      icon: Shield,
      title: "🔒 Secure Upload & Access",
      description: "Make sure documents are securely stored and processed — add user authentication if needed.",
      color: "text-indigo-400"
    },
    {
      icon: Download,
      title: "🧾 Export Chat History as PDF",
      description: "Allow users to export their conversation with the chatbot as a well-formatted PDF.",
      color: "text-emerald-400"
    }
  ];

  return (
    <div className="min-h-screen bg-navy-900">
      <Navbar />
      
      <div className="relative overflow-hidden">
        {/* Hero Section */}
        <div className="relative z-10 pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
                🌟 Suggested Features for Your 
                <span className="text-pink-500"> Docu Chatbot</span>
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
                Powerful features to enhance your document interaction experience and create the ultimate AI-powered document assistant.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300 group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex items-center mb-6">
                      <IconComponent className={`h-10 w-10 ${feature.color} mr-4 group-hover:scale-110 transition-transform duration-300`} />
                      <h3 className="text-xl font-bold text-white group-hover:text-pink-300 transition-colors duration-300">
                        {feature.title}
                      </h3>
                    </div>
                    <p className="text-gray-300 leading-relaxed">
                      {feature.description}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="relative z-10 py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="bg-gradient-to-r from-pink-500/20 to-purple-600/20 backdrop-blur-sm rounded-2xl p-12 border border-pink-500/30"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Build Your Document Chatbot?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Start creating intelligent chatbots that can understand and interact with your documents. 
                Join thousands of users who are already transforming their document workflows.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/register"
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  Get Started Free
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Link>
                <Link
                  to="/dashboard"
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-pink-400 border-2 border-pink-400 rounded-lg hover:bg-pink-400 hover:text-white transition-all duration-200"
                >
                  View Dashboard
                </Link>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-3 h-3 rounded-full bg-pink-400/30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturesPage;
