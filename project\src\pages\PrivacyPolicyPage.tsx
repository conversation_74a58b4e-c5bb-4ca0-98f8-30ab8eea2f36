import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Shield, 
  Users, 
  CheckCircle, 
  Trash2, 
  Search, 
  XCircle, 
  FileText, 
  Mail,
  ArrowLeft
} from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '../components/layout/Navbar';

const PrivacyPolicyPage: React.FC = () => {
  const userRights = [
    {
      icon: Users,
      title: "👥 Your Rights as a User",
      description: "You have the right to privacy — your documents and chat data are not shared with anyone.",
      color: "text-blue-400"
    },
    {
      icon: Trash2,
      title: "🗑️ Right to Deletion",
      description: "You have the right to request deletion of your uploaded files and chat history (if stored temporarily).",
      color: "text-red-400"
    },
    {
      icon: Search,
      title: "🔍 Right to Know",
      description: "You have the right to know what data is collected and how it's used — only the content you provide is processed.",
      color: "text-green-400"
    },
    {
      icon: XCircle,
      title: "⛔ Right to Withdraw",
      description: "You have the right to withdraw your consent and stop using the chatbot at any time.",
      color: "text-orange-400"
    },
    {
      icon: FileText,
      title: "📄 Right to Access",
      description: "You have the right to access any information stored during your session.",
      color: "text-purple-400"
    },
    {
      icon: Mail,
      title: "✉️ Right to Complain",
      description: "You have the right to file a complaint or ask questions by contacting us at [Your Email/Support Link].",
      color: "text-cyan-400"
    }
  ];

  return (
    <div className="min-h-screen bg-navy-900">
      <Navbar />
      
      <div className="relative overflow-hidden">
        {/* Hero Section */}
        <div className="relative z-10 pt-20 pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center mb-6">
                <Shield className="h-16 w-16 text-pink-400 mr-4" />
                <h1 className="text-5xl md:text-6xl font-bold text-white">
                  Privacy Policy
                </h1>
              </div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Your privacy and data protection are our top priorities. Learn about your rights and how we protect your information.
              </p>
            </motion.div>

            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <Link
                to="/"
                className="inline-flex items-center text-pink-400 hover:text-pink-300 transition-colors duration-200"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Home
              </Link>
            </motion.div>
          </div>
        </div>

        {/* User Rights Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Your Rights as a User
              </h2>
              <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                We believe in transparency and user control. Here are your fundamental rights when using our platform.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {userRights.map((right, index) => {
                const IconComponent = right.icon;
                return (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300 group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex items-start mb-4">
                      <IconComponent className={`h-8 w-8 ${right.color} mr-4 mt-1 group-hover:scale-110 transition-transform duration-300`} />
                      <div>
                        <h3 className="text-xl font-bold text-white group-hover:text-pink-300 transition-colors duration-300 mb-3">
                          {right.title}
                        </h3>
                        <p className="text-gray-300 leading-relaxed">
                          {right.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="bg-gradient-to-r from-pink-500/20 to-purple-600/20 backdrop-blur-sm rounded-2xl p-8 border border-pink-500/30"
            >
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  We're Committed to Your Privacy
                </h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Our platform is designed with privacy by design principles. We only process the documents you explicitly upload, 
                  and we never share your data with third parties. Your conversations and documents remain private and secure.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    to="/contact"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-white bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200"
                  >
                    <Mail className="h-5 w-5 mr-2" />
                    Contact Support
                  </Link>
                  <Link
                    to="/terms"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-pink-400 border-2 border-pink-400 rounded-lg hover:bg-pink-400 hover:text-white transition-all duration-200"
                  >
                    View Terms of Service
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 rounded-full bg-pink-400/20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
