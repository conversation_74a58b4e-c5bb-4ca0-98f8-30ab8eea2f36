{"version": 3, "file": "react-router.development.js", "sources": ["../lib/context.ts", "../lib/hooks.tsx", "../lib/deprecations.ts", "../lib/components.tsx", "../index.ts"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n  viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type { FutureConfig as RouterFutureConfig } from \"@remix-run/router\";\nimport type { FutureConfig as RenderFutureConfig } from \"./components\";\n\nconst alreadyWarned: { [key: string]: boolean } = {};\n\nexport function warnOnce(key: string, message: string): void {\n  if (__DEV__ && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nconst logDeprecation = (flag: string, msg: string, link: string) =>\n  warnOnce(\n    flag,\n    `⚠️ React Router Future Flag Warning: ${msg}. ` +\n      `You can use the \\`${flag}\\` future flag to opt-in early. ` +\n      `For more information, see ${link}.`\n  );\n\nexport function logV6DeprecationWarnings(\n  renderFuture: Partial<RenderFutureConfig> | undefined,\n  routerFuture?: Omit<RouterFutureConfig, \"v7_prependBasename\">\n) {\n  if (renderFuture?.v7_startTransition === undefined) {\n    logDeprecation(\n      \"v7_startTransition\",\n      \"React Router will begin wrapping state updates in `React.startTransition` in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\"\n    );\n  }\n\n  if (\n    renderFuture?.v7_relativeSplatPath === undefined &&\n    (!routerFuture || routerFuture.v7_relativeSplatPath === undefined)\n  ) {\n    logDeprecation(\n      \"v7_relativeSplatPath\",\n      \"Relative route resolution within Splat routes is changing in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\"\n    );\n  }\n\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\n        \"v7_fetcherPersist\",\n        \"The persistence behavior of fetchers is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\"\n      );\n    }\n\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\n        \"v7_normalizeFormMethod\",\n        \"Casing of `formMethod` fields is being normalized to uppercase in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\"\n      );\n    }\n\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\n        \"v7_partialHydration\",\n        \"`RouterProvider` hydration behavior is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\"\n      );\n    }\n\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\n        \"v7_skipActionErrorRevalidation\",\n        \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\"\n      );\n    }\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\nimport { logV6DeprecationWarnings } from \"./deprecations\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [router, future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AgnosticPatchRoutesOnNavigationFunction,\n  AgnosticPatchRoutesOnNavigationFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\nimport { logV6DeprecationWarnings } from \"./lib/deprecations\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport type PatchRoutesOnNavigationFunctionArgs =\n  AgnosticPatchRoutesOnNavigationFunctionArgs<RouteObject, RouteMatch>;\n\nexport type PatchRoutesOnNavigationFunction =\n  AgnosticPatchRoutesOnNavigationFunction<RouteObject, RouteMatch>;\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    dataStrategy?: DataStrategyFunction;\n    patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n  logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings,\n};\n"], "names": ["DataRouterContext", "React", "createContext", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "decodePath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "process", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "warnOnce", "warn", "logDeprecation", "flag", "msg", "link", "logV6DeprecationWarnings", "renderFuture", "routerFuture", "v7_startTransition", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_skipActionErrorRevalidation", "START_TRANSITION", "startTransitionImpl", "RouterProvider", "fallbackElement", "setStateImpl", "setState", "newState", "subscribe", "n", "opts", "preventScrollReset", "Router", "DataRoutes", "MemoryRouter", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "jsonPath", "Outlet", "Route", "_props", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "Routes", "createRoutesFromChildren", "Await", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "data", "Aborted<PERSON>eferredError", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "name", "caseSensitive", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "renderMatches", "mapRouteProperties", "updates", "createMemoryRouter", "createRouter", "v7_prependBasename", "hydrationData", "dataStrategy", "patchRoutesOnNavigation", "initialize"], "mappings": ";;;;;;;;;;;;;;AAgFO,MAAMA,iBAAiB,gBAC5BC,KAAK,CAACC,aAAa,CAAiC,IAAI,EAAC;AAC9C;EACXF,iBAAiB,CAACG,WAAW,GAAG,YAAY,CAAA;AAC7C,CAAA;AAEM,MAAMC,sBAAsB,gBAAGH,KAAK,CAACC,aAAa,CAEvD,IAAI,EAAC;AACM;EACXE,sBAAsB,CAACD,WAAW,GAAG,iBAAiB,CAAA;AACvD,CAAA;AAEM,MAAME,YAAY,gBAAGJ,KAAK,CAACC,aAAa,CAAwB,IAAI,CAAC,CAAA;AAC/D;EACXG,YAAY,CAACF,WAAW,GAAG,OAAO,CAAA;AACnC,CAAA;AAsCM,MAAMG,iBAAiB,gBAAGL,KAAK,CAACC,aAAa,CAClD,IAAK,EACN;AAEY;EACXI,iBAAiB,CAACH,WAAW,GAAG,YAAY,CAAA;AAC7C,CAAA;AAOM,MAAMI,eAAe,gBAAGN,KAAK,CAACC,aAAa,CAChD,IAAK,EACN;AAEY;EACXK,eAAe,CAACJ,WAAW,GAAG,UAAU,CAAA;AACzC,CAAA;MAQYK,YAAY,gBAAGP,KAAK,CAACC,aAAa,CAAqB;AAClEO,EAAAA,MAAM,EAAE,IAAI;AACZC,EAAAA,OAAO,EAAE,EAAE;AACXC,EAAAA,WAAW,EAAE,KAAA;AACd,CAAA,EAAC;AAEW;EACXH,YAAY,CAACL,WAAW,GAAG,OAAO,CAAA;AACnC,CAAA;AAEM,MAAMS,iBAAiB,gBAAGX,KAAK,CAACC,aAAa,CAAM,IAAI,CAAC,CAAA;AAElD;EACXU,iBAAiB,CAACT,WAAW,GAAG,YAAY,CAAA;AAC7C;;AC7HD;;;;;AAKG;AACG,SAAUU,OAAOA,CACrBC,EAAM,EACN;AAAEC,EAAAA,QAAAA;IAAiD,EAAE,EAAA;EAErD,CACEC,kBAAkB,EAAE,GADtBC,gBAAS,CAAA,KAAA;AAEP;AACA;AACA,EAAA,CAAA,kEAAA,CAAoE,EAJ7D,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;IAAEC,QAAQ;AAAEC,IAAAA,SAAAA;AAAW,GAAA,GAAGlB,KAAK,CAACmB,UAAU,CAACd,iBAAiB,CAAC,CAAA;EACjE,IAAI;IAAEe,IAAI;IAAEC,QAAQ;AAAEC,IAAAA,MAAAA;AAAQ,GAAA,GAAGC,eAAe,CAACV,EAAE,EAAE;AAAEC,IAAAA,QAAAA;AAAQ,GAAE,CAAC,CAAA;EAElE,IAAIU,cAAc,GAAGH,QAAQ,CAAA;AAE7B;AACA;AACA;AACA;EACA,IAAIJ,QAAQ,KAAK,GAAG,EAAE;AACpBO,IAAAA,cAAc,GACZH,QAAQ,KAAK,GAAG,GAAGJ,QAAQ,GAAGQ,SAAS,CAAC,CAACR,QAAQ,EAAEI,QAAQ,CAAC,CAAC,CAAA;AAChE,GAAA;EAED,OAAOH,SAAS,CAACQ,UAAU,CAAC;AAAEL,IAAAA,QAAQ,EAAEG,cAAc;IAAEF,MAAM;AAAEF,IAAAA,IAAAA;AAAI,GAAE,CAAC,CAAA;AACzE,CAAA;AAEA;;;;AAIG;SACaL,kBAAkBA,GAAA;AAChC,EAAA,OAAOf,KAAK,CAACmB,UAAU,CAACb,eAAe,CAAC,IAAI,IAAI,CAAA;AAClD,CAAA;AAEA;;;;;;;;;AASG;SACaqB,WAAWA,GAAA;EACzB,CACEZ,kBAAkB,EAAE,GADtBC,gBAAS,CAAA,KAAA;AAEP;AACA;AACA,EAAA,CAAA,sEAAA,CAAwE,EAJjE,GAAA,KAAA,CAAA,CAAA;AAOT,EAAA,OAAOhB,KAAK,CAACmB,UAAU,CAACb,eAAe,CAAC,CAACsB,QAAQ,CAAA;AACnD,CAAA;AAEA;;;;;AAKG;SACaC,iBAAiBA,GAAA;AAC/B,EAAA,OAAO7B,KAAK,CAACmB,UAAU,CAACb,eAAe,CAAC,CAACwB,cAAc,CAAA;AACzD,CAAA;AAEA;;;;;;AAMG;AACG,SAAUC,QAAQA,CAGtBC,OAAiC,EAAA;EACjC,CACEjB,kBAAkB,EAAE,GADtBC,gBAAS,CAAA,KAAA;AAEP;AACA;AACA,EAAA,CAAA,mEAAA,CAAqE,EAJ9D,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;AAAEK,IAAAA,QAAAA;GAAU,GAAGM,WAAW,EAAE,CAAA;EAChC,OAAO3B,KAAK,CAACiC,OAAO,CAClB,MAAMC,SAAS,CAAiBF,OAAO,EAAEG,iBAAU,CAACd,QAAQ,CAAC,CAAC,EAC9D,CAACA,QAAQ,EAAEW,OAAO,CAAC,CACpB,CAAA;AACH,CAAA;AAUA,MAAMI,qBAAqB,GACqC,CAAA,4DAAA,CAAA,GAC9D,CAAmC,iCAAA,CAAA,CAAA;AAErC;AACA,SAASC,yBAAyBA,CAChCC,EAA+C,EAAA;EAE/C,IAAIC,QAAQ,GAAGvC,KAAK,CAACmB,UAAU,CAACd,iBAAiB,CAAC,CAACmC,MAAM,CAAA;EACzD,IAAI,CAACD,QAAQ,EAAE;AACb;AACA;AACA;AACAvC,IAAAA,KAAK,CAACyC,eAAe,CAACH,EAAE,CAAC,CAAA;AAC1B,GAAA;AACH,CAAA;AAEA;;;;;AAKG;SACaI,WAAWA,GAAA;EACzB,IAAI;AAAEhC,IAAAA,WAAAA;AAAW,GAAE,GAAGV,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;AACpD;AACA;EACA,OAAOG,WAAW,GAAGiC,iBAAiB,EAAE,GAAGC,mBAAmB,EAAE,CAAA;AAClE,CAAA;AAEA,SAASA,mBAAmBA,GAAA;EAC1B,CACE7B,kBAAkB,EAAE,GADtBC,gBAAS,CAAA,KAAA;AAEP;AACA;AACA,EAAA,CAAA,sEAAA,CAAwE,EAJjE,GAAA,KAAA,CAAA,CAAA;AAOT,EAAA,IAAI6B,iBAAiB,GAAG7C,KAAK,CAACmB,UAAU,CAACpB,iBAAiB,CAAC,CAAA;EAC3D,IAAI;IAAEkB,QAAQ;IAAE6B,MAAM;AAAE5B,IAAAA,SAAAA;AAAS,GAAE,GAAGlB,KAAK,CAACmB,UAAU,CAACd,iBAAiB,CAAC,CAAA;EACzE,IAAI;AAAEI,IAAAA,OAAAA;AAAO,GAAE,GAAGT,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;EAChD,IAAI;AAAEc,IAAAA,QAAQ,EAAE0B,gBAAAA;GAAkB,GAAGpB,WAAW,EAAE,CAAA;AAElD,EAAA,IAAIqB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAAC,CAC1D,CAAA;AAED,EAAA,IAAIC,SAAS,GAAGrD,KAAK,CAACsD,MAAM,CAAC,KAAK,CAAC,CAAA;AACnCjB,EAAAA,yBAAyB,CAAC,MAAK;IAC7BgB,SAAS,CAACE,OAAO,GAAG,IAAI,CAAA;AAC1B,GAAC,CAAC,CAAA;AAEF,EAAA,IAAIC,QAAQ,GAAqBxD,KAAK,CAACyD,WAAW,CAChD,CAAC5C,EAAe,EAAE6C,OAAA,GAA2B,EAAE,KAAI;AACjDC,IAAAC,cAAO,CAACP,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC,CAAA,CAAA;AAEjD;AACA;AACA,IAAA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE,OAAA;AAExB,IAAA,IAAI,OAAO1C,EAAE,KAAK,QAAQ,EAAE;AAC1BK,MAAAA,SAAS,CAAC2C,EAAE,CAAChD,EAAE,CAAC,CAAA;AAChB,MAAA,OAAA;AACD,KAAA;IAED,IAAIiD,IAAI,GAAGC,SAAS,CAClBlD,EAAE,EACFoC,IAAI,CAACe,KAAK,CAAChB,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBW,OAAO,CAAC5C,QAAQ,KAAK,MAAM,CAC5B,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI+B,iBAAiB,IAAI,IAAI,IAAI5B,QAAQ,KAAK,GAAG,EAAE;MACjD6C,IAAI,CAACzC,QAAQ,GACXyC,IAAI,CAACzC,QAAQ,KAAK,GAAG,GACjBJ,QAAQ,GACRQ,SAAS,CAAC,CAACR,QAAQ,EAAE6C,IAAI,CAACzC,QAAQ,CAAC,CAAC,CAAA;AAC3C,KAAA;IAED,CAAC,CAAC,CAACqC,OAAO,CAACO,OAAO,GAAG/C,SAAS,CAAC+C,OAAO,GAAG/C,SAAS,CAACgD,IAAI,EACrDJ,IAAI,EACJJ,OAAO,CAACS,KAAK,EACbT,OAAO,CACR,CAAA;AACH,GAAC,EACD,CACEzC,QAAQ,EACRC,SAAS,EACT8B,kBAAkB,EAClBD,gBAAgB,EAChBF,iBAAiB,CAClB,CACF,CAAA;AAED,EAAA,OAAOW,QAAQ,CAAA;AACjB,CAAA;AAEA,MAAMY,aAAa,gBAAGpE,KAAK,CAACC,aAAa,CAAU,IAAI,CAAC,CAAA;AAExD;;;;AAIG;SACaoE,gBAAgBA,GAAA;AAC9B,EAAA,OAAOrE,KAAK,CAACmB,UAAU,CAACiD,aAAa,CAAY,CAAA;AACnD,CAAA;AAEA;;;;;AAKG;AACG,SAAUE,SAASA,CAACC,OAAiB,EAAA;EACzC,IAAI/D,MAAM,GAAGR,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAACC,MAAM,CAAA;AAClD,EAAA,IAAIA,MAAM,EAAE;AACV,IAAA,oBACER,KAAA,CAAAwE,aAAA,CAACJ,aAAa,CAACK,QAAQ,EAAA;AAACC,MAAAA,KAAK,EAAEH,OAAAA;KAAO,EAAG/D,MAAM,CAA0B,CAAA;AAE5E,GAAA;AACD,EAAA,OAAOA,MAAM,CAAA;AACf,CAAA;AAEA;;;;;AAKG;SACamE,SAASA,GAAA;EAKvB,IAAI;AAAElE,IAAAA,OAAAA;AAAO,GAAE,GAAGT,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;EAChD,IAAIqE,UAAU,GAAGnE,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAAA;AAC5C,EAAA,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAc,GAAG,EAAE,CAAA;AACrD,CAAA;AAEA;;;;AAIG;AACG,SAAUvD,eAAeA,CAC7BV,EAAM,EACN;AAAEC,EAAAA,QAAAA;IAAiD,EAAE,EAAA;EAErD,IAAI;AAAEgC,IAAAA,MAAAA;AAAM,GAAE,GAAG9C,KAAK,CAACmB,UAAU,CAACd,iBAAiB,CAAC,CAAA;EACpD,IAAI;AAAEI,IAAAA,OAAAA;AAAO,GAAE,GAAGT,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;EAChD,IAAI;AAAEc,IAAAA,QAAQ,EAAE0B,gBAAAA;GAAkB,GAAGpB,WAAW,EAAE,CAAA;AAClD,EAAA,IAAIqB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAAC,CAC1D,CAAA;AAED,EAAA,OAAOpD,KAAK,CAACiC,OAAO,CAClB,MACE8B,SAAS,CACPlD,EAAE,EACFoC,IAAI,CAACe,KAAK,CAAChB,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBjC,QAAQ,KAAK,MAAM,CACpB,EACH,CAACD,EAAE,EAAEmC,kBAAkB,EAAED,gBAAgB,EAAEjC,QAAQ,CAAC,CACrD,CAAA;AACH,CAAA;AAEA;;;;;;;AAOG;AACa,SAAAiE,SAASA,CACvBC,MAAqB,EACrBC,WAAwC,EAAA;AAExC,EAAA,OAAOC,aAAa,CAACF,MAAM,EAAEC,WAAW,CAAC,CAAA;AAC3C,CAAA;AAEA;AACM,SAAUC,aAAaA,CAC3BF,MAAqB,EACrBC,WAAwC,EACxCE,eAAsC,EACtCrC,MAA8B,EAAA;EAE9B,CACE/B,kBAAkB,EAAE,GADtBC,gBAAS,CAAA,KAAA;AAEP;AACA;AACA,EAAA,CAAA,oEAAA,CAAsE,EAJ/D,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;AAAEE,IAAAA,SAAAA;AAAS,GAAE,GAAGlB,KAAK,CAACmB,UAAU,CAACd,iBAAiB,CAAC,CAAA;EACvD,IAAI;AAAEI,IAAAA,OAAO,EAAE2E,aAAAA;AAAe,GAAA,GAAGpF,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;EAC/D,IAAIqE,UAAU,GAAGQ,aAAa,CAACA,aAAa,CAACP,MAAM,GAAG,CAAC,CAAC,CAAA;EACxD,IAAIQ,YAAY,GAAGT,UAAU,GAAGA,UAAU,CAACE,MAAM,GAAG,EAAE,CAAA;EACtD,IAAIQ,cAAc,GAAGV,UAAU,GAAGA,UAAU,CAACvD,QAAQ,GAAG,GAAG,CAAA;EAC3D,IAAIkE,kBAAkB,GAAGX,UAAU,GAAGA,UAAU,CAACY,YAAY,GAAG,GAAG,CAAA;AACnE,EAAA,IAAIC,WAAW,GAAGb,UAAU,IAAIA,UAAU,CAACc,KAAK,CAAA;AAEhD,EAAa;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,IAAIC,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAAC3B,IAAI,IAAK,EAAE,CAAA;AACxD8B,IAAAA,WAAW,CACTN,cAAc,EACd,CAACG,WAAW,IAAIE,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EAC0B,CAAA,gEAAA,CAAA,GAC5D,IAAAP,cAAuC,CAAA,sBAAA,EAAAK,UAAwB,CAAA,YAAA,CAAA,GACC,CAAA,kEAAA,CAAA,GACH,iEAAA,GAClC,CAAA,6BAAA,CAAA,GAC/B,CAAyCA,sCAAAA,EAAAA,UAAyB,eAAA,GAClE,CAAA,MAAA,EAASA,UAAU,KAAK,GAAG,GAAG,GAAG,GAAM,GAAAA,UAAc,CAAA,EAAA,CAAA,KAAK,CAC7D,CAAA;AACF,GAAA;AAED,EAAA,IAAIG,mBAAmB,GAAGnE,WAAW,EAAE,CAAA;AAEvC,EAAA,IAAIC,QAAQ,CAAA;AACZ,EAAA,IAAIqD,WAAW,EAAE;AACf,IAAA,IAAIc,iBAAiB,GACnB,OAAOd,WAAW,KAAK,QAAQ,GAAGe,SAAS,CAACf,WAAW,CAAC,GAAGA,WAAW,CAAA;AAExE,IAAA,EACEM,kBAAkB,KAAK,GAAG,IACxBQ,iBAAiB,CAAC1E,QAAQ,EAAE4E,UAAU,CAACV,kBAAkB,CAAC,CAAA5B,GAF9D3C,gBAAS,CAGwF,KAAA,EAAA,CAAA,6FAAA,CAAA,GACZ,CAAA,+EAAA,CAAA,GACjF,CAA+DuE,4DAAAA,EAAAA,kBAAsB,CAAA,EAAA,CAAA,GACrF,iBAAiBQ,iBAAiB,CAAC1E,QAAQ,CAAA,qCAAA,CAAuC,EAN7E,GAAA,KAAA,CAAA,CAAA;AASTO,IAAAA,QAAQ,GAAGmE,iBAAiB,CAAA;AAC7B,GAAA,MAAM;AACLnE,IAAAA,QAAQ,GAAGkE,mBAAmB,CAAA;AAC/B,GAAA;AAED,EAAA,IAAIzE,QAAQ,GAAGO,QAAQ,CAACP,QAAQ,IAAI,GAAG,CAAA;EAEvC,IAAI6E,iBAAiB,GAAG7E,QAAQ,CAAA;EAChC,IAAIkE,kBAAkB,KAAK,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIY,cAAc,GAAGZ,kBAAkB,CAACtB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACmC,KAAK,CAAC,GAAG,CAAC,CAAA;AACrE,IAAA,IAAIC,QAAQ,GAAGhF,QAAQ,CAAC4C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACmC,KAAK,CAAC,GAAG,CAAC,CAAA;AACrDF,IAAAA,iBAAiB,GAAG,GAAG,GAAGG,QAAQ,CAACC,KAAK,CAACH,cAAc,CAACtB,MAAM,CAAC,CAAC0B,IAAI,CAAC,GAAG,CAAC,CAAA;AAC1E,GAAA;AAED,EAAA,IAAI9F,OAAO,GAAG+F,WAAW,CAACxB,MAAM,EAAE;AAAE3D,IAAAA,QAAQ,EAAE6E,iBAAAA;AAAmB,GAAA,CAAC,CAAA;AAElE,EAAa;IACXtC,cAAO,CACL6B,WAAW,IAAIhF,OAAO,IAAI,IAAI,iCACCmB,QAAQ,CAACP,QAAW,CAAA,EAAAO,QAAQ,CAACN,MAAM,CAAA,EAAGM,QAAQ,CAACR,IAAQ,CAAA,EAAA,CAAA,CACvF,CAAA,CAAA;AAEDuC,IAAAC,cAAO,CACLnD,OAAO,IAAI,IAAI,IACbA,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAACa,KAAK,CAACe,OAAO,KAAKC,SAAS,IACvDjG,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAACa,KAAK,CAACiB,SAAS,KAAKD,SAAS,IACzDjG,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAACa,KAAK,CAACkB,IAAI,KAAKF,SAAS,EACnB,CAAA,gCAAA,EAAA9E,QAAQ,CAACP,QAAW,CAAAO,EAAAA,QAAQ,CAACN,MAAS,CAAA,EAAAM,QAAQ,CAACR,IAAQ,CAAA,EAAA,CAAA,GACA,CAAA,sFAAA,CAAA,GACxF,qDAAqD,CACxD,CAAA,CAAA;AACF,GAAA;EAED,IAAIyF,eAAe,GAAGC,cAAc,CAClCrG,OAAO,IACLA,OAAO,CAACsG,GAAG,CAAEC,KAAK,IAChBC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEF,KAAK,EAAE;AACvBlC,IAAAA,MAAM,EAAEmC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE7B,YAAY,EAAE2B,KAAK,CAAClC,MAAM,CAAC;AACrDzD,IAAAA,QAAQ,EAAEI,SAAS,CAAC,CAClB8D,kBAAkB;AAClB;AACArE,IAAAA,SAAS,CAACiG,cAAc,GACpBjG,SAAS,CAACiG,cAAc,CAACH,KAAK,CAAC3F,QAAQ,CAAC,CAACA,QAAQ,GACjD2F,KAAK,CAAC3F,QAAQ,CACnB,CAAC;AACFmE,IAAAA,YAAY,EACVwB,KAAK,CAACxB,YAAY,KAAK,GAAG,GACtBD,kBAAkB,GAClB9D,SAAS,CAAC,CACR8D,kBAAkB;AAClB;AACArE,IAAAA,SAAS,CAACiG,cAAc,GACpBjG,SAAS,CAACiG,cAAc,CAACH,KAAK,CAACxB,YAAY,CAAC,CAACnE,QAAQ,GACrD2F,KAAK,CAACxB,YAAY,CACvB,CAAA;GACR,CAAC,CACH,EACHJ,aAAa,EACbD,eAAe,EACfrC,MAAM,CACP,CAAA;AAED;AACA;AACA;EACA,IAAImC,WAAW,IAAI4B,eAAe,EAAE;AAClC,IAAA,oBACE7G,KAAC,CAAAwE,aAAA,CAAAlE,eAAe,CAACmE,QAAQ,EAAA;AACvBC,MAAAA,KAAK,EAAE;AACL9C,QAAAA,QAAQ,EAAE;AACRP,UAAAA,QAAQ,EAAE,GAAG;AACbC,UAAAA,MAAM,EAAE,EAAE;AACVF,UAAAA,IAAI,EAAE,EAAE;AACR+C,UAAAA,KAAK,EAAE,IAAI;AACXiD,UAAAA,GAAG,EAAE,SAAS;UACd,GAAGxF,QAAAA;SACJ;QACDE,cAAc,EAAEuF,MAAc,CAACC,GAAAA;;KAGhC,EAAAT,eAAe,CACS,CAAA;AAE9B,GAAA;AAED,EAAA,OAAOA,eAAe,CAAA;AACxB,CAAA;AAEA,SAASU,qBAAqBA,GAAA;AAC5B,EAAA,IAAIC,KAAK,GAAGC,aAAa,EAAE,CAAA;AAC3B,EAAA,IAAIC,OAAO,GAAGC,oBAAoB,CAACH,KAAK,CAAC,MAClCA,KAAK,CAACI,MAAU,CAAA,CAAA,EAAAJ,KAAK,CAACK,UAAY,CAAA,CAAA,GACrCL,KAAK,YAAYM,KAAK,GACtBN,KAAK,CAACE,OAAO,GACbzE,IAAI,CAACC,SAAS,CAACsE,KAAK,CAAC,CAAA;EACzB,IAAIO,KAAK,GAAGP,KAAK,YAAYM,KAAK,GAAGN,KAAK,CAACO,KAAK,GAAG,IAAI,CAAA;EACvD,IAAIC,SAAS,GAAG,wBAAwB,CAAA;AACxC,EAAA,IAAIC,SAAS,GAAG;AAAEC,IAAAA,OAAO,EAAE,QAAQ;AAAEC,IAAAA,eAAe,EAAEH,SAAAA;GAAW,CAAA;AACjE,EAAA,IAAII,UAAU,GAAG;AAAEF,IAAAA,OAAO,EAAE,SAAS;AAAEC,IAAAA,eAAe,EAAEH,SAAAA;GAAW,CAAA;EAEnE,IAAIK,OAAO,GAAG,IAAI,CAAA;AAClB,EAAa;AACXC,IAAAA,OAAO,CAACd,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN,CAAA;AAEDa,IAAAA,OAAO,gBACLrI,KAAA,CAAAwE,aAAA,CAAAxE,KAAA,CAAAuI,QAAA,EAAA,IAAA,eACEvI,KAA0B,CAAAwE,aAAA,CAAA,GAAA,EAAA,IAAA,EAAA,yCAAA,CAAA,eAC1BxE,KAAA,CAAAwE,aAAA,CAAA,GAAA,EAAA,IAAA,+GAEwBxE,KAAM,CAAAwE,aAAA,CAAA,MAAA,EAAA;AAAAgE,MAAAA,KAAK,EAAEJ,UAAAA;AAAgC,KAAA,EAAA,eAAA,CAAA,SAAI,GAAG,eAC1EpI,KAAM,CAAAwE,aAAA,CAAA,MAAA,EAAA;AAAAgE,MAAAA,KAAK,EAAEJ,UAAAA;AAA+B,KAAA,EAAA,cAAA,CAAA,EAC1C,sBAAA,CAAA,CAEP,CAAA;AACF,GAAA;EAED,oBACEpI,KAAA,CAAAwE,aAAA,CAAAxE,KAAA,CAAAuI,QAAA,EAAA,IAAA,eACEvI,KAAsC,CAAAwE,aAAA,CAAA,IAAA,EAAA,IAAA,EAAA,+BAAA,CAAA,eACtCxE,KAAI,CAAAwE,aAAA,CAAA,IAAA,EAAA;AAAAgE,IAAAA,KAAK,EAAE;AAAEC,MAAAA,SAAS,EAAE,QAAA;AAAQ,KAAA;GAAK,EAAAf,OAAO,CAAM,EACjDK,KAAK,gBAAG/H,KAAA,CAAAwE,aAAA,CAAA,KAAA,EAAA;AAAKgE,IAAAA,KAAK,EAAEP,SAAAA;AAAS,GAAA,EAAGF,KAAK,CAAO,GAAG,IAAI,EACnDM,OAAO,CACP,CAAA;AAEP,CAAA;AAEA,MAAMK,mBAAmB,gBAAG1I,KAAC,CAAAwE,aAAA,CAAA+C,qBAAqB,OAAG,CAAA;AAgBxC,MAAAoB,mBAAoB,SAAQ3I,KAAK,CAAC2G,SAG9C,CAAA;EACCiC,WAAAA,CAAYC,KAA+B,EAAA;IACzC,KAAK,CAACA,KAAK,CAAC,CAAA;IACZ,IAAI,CAAC1E,KAAK,GAAG;MACXvC,QAAQ,EAAEiH,KAAK,CAACjH,QAAQ;MACxBkH,YAAY,EAAED,KAAK,CAACC,YAAY;MAChCtB,KAAK,EAAEqB,KAAK,CAACrB,KAAAA;KACd,CAAA;AACH,GAAA;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAA;IACxC,OAAO;AAAEA,MAAAA,KAAK,EAAEA,KAAAA;KAAO,CAAA;AACzB,GAAA;AAEA,EAAA,OAAOwB,wBAAwBA,CAC7BH,KAA+B,EAC/B1E,KAA+B,EAAA;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACEA,KAAK,CAACvC,QAAQ,KAAKiH,KAAK,CAACjH,QAAQ,IAChCuC,KAAK,CAAC2E,YAAY,KAAK,MAAM,IAAID,KAAK,CAACC,YAAY,KAAK,MAAO,EAChE;MACA,OAAO;QACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK;QAClB5F,QAAQ,EAAEiH,KAAK,CAACjH,QAAQ;QACxBkH,YAAY,EAAED,KAAK,CAACC,YAAAA;OACrB,CAAA;AACF,KAAA;AAED;AACA;AACA;AACA;IACA,OAAO;AACLtB,MAAAA,KAAK,EAAEqB,KAAK,CAACrB,KAAK,KAAKd,SAAS,GAAGmC,KAAK,CAACrB,KAAK,GAAGrD,KAAK,CAACqD,KAAK;MAC5D5F,QAAQ,EAAEuC,KAAK,CAACvC,QAAQ;AACxBkH,MAAAA,YAAY,EAAED,KAAK,CAACC,YAAY,IAAI3E,KAAK,CAAC2E,YAAAA;KAC3C,CAAA;AACH,GAAA;AAEAG,EAAAA,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAA;IAC1CZ,OAAO,CAACd,KAAK,CACX,uDAAuD,EACvDA,KAAK,EACL0B,SAAS,CACV,CAAA;AACH,GAAA;AAEAC,EAAAA,MAAMA,GAAA;AACJ,IAAA,OAAO,IAAI,CAAChF,KAAK,CAACqD,KAAK,KAAKd,SAAS,gBACnC1G,oBAACO,YAAY,CAACkE,QAAQ,EAAC;AAAAC,MAAAA,KAAK,EAAE,IAAI,CAACmE,KAAK,CAACO,YAAAA;KAAY,eACnDpJ,KAAA,CAAAwE,aAAA,CAAC7D,iBAAiB,CAAC8D,QAAQ,EAAA;AACzBC,MAAAA,KAAK,EAAE,IAAI,CAACP,KAAK,CAACqD,KAAK;AACvB6B,MAAAA,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACS,SAAAA;AAAS,KAAA,CAC9B,CACoB,GAExB,IAAI,CAACT,KAAK,CAACQ,QACZ,CAAA;AACH,GAAA;AACD,CAAA;AAQD,SAASE,aAAaA,CAAC;EAAEH,YAAY;EAAEpC,KAAK;AAAEqC,EAAAA,QAAAA;AAA8B,CAAA,EAAA;AAC1E,EAAA,IAAIxG,iBAAiB,GAAG7C,KAAK,CAACmB,UAAU,CAACpB,iBAAiB,CAAC,CAAA;AAE3D;AACA;EACA,IACE8C,iBAAiB,IACjBA,iBAAiB,CAACL,MAAM,IACxBK,iBAAiB,CAAC2G,aAAa,KAC9BxC,KAAK,CAACtB,KAAK,CAAC+D,YAAY,IAAIzC,KAAK,CAACtB,KAAK,CAACgE,aAAa,CAAC,EACvD;IACA7G,iBAAiB,CAAC2G,aAAa,CAACG,0BAA0B,GAAG3C,KAAK,CAACtB,KAAK,CAACkE,EAAE,CAAA;AAC5E,GAAA;AAED,EAAA,oBACE5J,KAAA,CAAAwE,aAAA,CAACjE,YAAY,CAACkE,QAAQ,EAAA;AAACC,IAAAA,KAAK,EAAE0E,YAAAA;GAAY,EACvCC,QAAQ,CACa,CAAA;AAE5B,CAAA;AAEgB,SAAAvC,cAAcA,CAC5BrG,OAA4B,EAC5B2E,aAAA,GAA8B,EAAE,EAChCD,eAA+C,GAAA,IAAI,EACnDrC,MAAA,GAAuC,IAAI,EAAA;EAE3C,IAAIrC,OAAO,IAAI,IAAI,EAAE;IACnB,IAAI,CAAC0E,eAAe,EAAE;AACpB,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;IAED,IAAIA,eAAe,CAAC0E,MAAM,EAAE;AAC1B;AACA;MACApJ,OAAO,GAAG0E,eAAe,CAAC1E,OAA2B,CAAA;KACtD,MAAM,IACLqC,MAAM,EAAEgH,mBAAmB,IAC3B1E,aAAa,CAACP,MAAM,KAAK,CAAC,IAC1B,CAACM,eAAe,CAAC4E,WAAW,IAC5B5E,eAAe,CAAC1E,OAAO,CAACoE,MAAM,GAAG,CAAC,EAClC;AACA;AACA;AACA;AACA;AACA;AACA;MACApE,OAAO,GAAG0E,eAAe,CAAC1E,OAA2B,CAAA;AACtD,KAAA,MAAM;AACL,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;AACF,GAAA;EAED,IAAIoG,eAAe,GAAGpG,OAAO,CAAA;AAE7B;AACA,EAAA,IAAIoJ,MAAM,GAAG1E,eAAe,EAAE0E,MAAM,CAAA;EACpC,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,IAAIG,UAAU,GAAGnD,eAAe,CAACoD,SAAS,CACvCC,CAAC,IAAKA,CAAC,CAACxE,KAAK,CAACkE,EAAE,IAAIC,MAAM,GAAGK,CAAC,CAACxE,KAAK,CAACkE,EAAE,CAAC,KAAKlD,SAAS,CACxD,CAAA;IACD,EACEsD,UAAU,IAAI,CAAC,CAAArG,GADjB3C,gBAAS,CAEP,KAAA,EAAA,CAAA,yDAAA,EAA4DiG,MAAM,CAACkD,IAAI,CACrEN,MAAM,CACP,CAACtD,IAAI,CAAC,GAAG,CAAC,CAAE,CAAA,CAAA,CAJN,GAAA,KAAA,CAAA,CAAA;AAMTM,IAAAA,eAAe,GAAGA,eAAe,CAACP,KAAK,CACrC,CAAC,EACD8D,IAAI,CAACC,GAAG,CAACxD,eAAe,CAAChC,MAAM,EAAEmF,UAAU,GAAG,CAAC,CAAC,CACjD,CAAA;AACF,GAAA;AAED;AACA;EACA,IAAIM,cAAc,GAAG,KAAK,CAAA;EAC1B,IAAIC,aAAa,GAAG,CAAC,CAAC,CAAA;AACtB,EAAA,IAAIpF,eAAe,IAAIrC,MAAM,IAAIA,MAAM,CAACgH,mBAAmB,EAAE;AAC3D,IAAA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,eAAe,CAAChC,MAAM,EAAE2F,CAAC,EAAE,EAAE;AAC/C,MAAA,IAAIxD,KAAK,GAAGH,eAAe,CAAC2D,CAAC,CAAC,CAAA;AAC9B;MACA,IAAIxD,KAAK,CAACtB,KAAK,CAAC+E,eAAe,IAAIzD,KAAK,CAACtB,KAAK,CAACgF,sBAAsB,EAAE;AACrEH,QAAAA,aAAa,GAAGC,CAAC,CAAA;AAClB,OAAA;AAED,MAAA,IAAIxD,KAAK,CAACtB,KAAK,CAACkE,EAAE,EAAE;QAClB,IAAI;UAAEe,UAAU;AAAEd,UAAAA,MAAM,EAANA,OAAAA;AAAQ,SAAA,GAAG1E,eAAe,CAAA;AAC5C,QAAA,IAAIyF,gBAAgB,GAClB5D,KAAK,CAACtB,KAAK,CAACmF,MAAM,IAClBF,UAAU,CAAC3D,KAAK,CAACtB,KAAK,CAACkE,EAAE,CAAC,KAAKlD,SAAS,KACvC,CAACmD,OAAM,IAAIA,OAAM,CAAC7C,KAAK,CAACtB,KAAK,CAACkE,EAAE,CAAC,KAAKlD,SAAS,CAAC,CAAA;AACnD,QAAA,IAAIM,KAAK,CAACtB,KAAK,CAACkB,IAAI,IAAIgE,gBAAgB,EAAE;AACxC;AACA;AACA;AACAN,UAAAA,cAAc,GAAG,IAAI,CAAA;UACrB,IAAIC,aAAa,IAAI,CAAC,EAAE;YACtB1D,eAAe,GAAGA,eAAe,CAACP,KAAK,CAAC,CAAC,EAAEiE,aAAa,GAAG,CAAC,CAAC,CAAA;AAC9D,WAAA,MAAM;AACL1D,YAAAA,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;AACvC,WAAA;AACD,UAAA,MAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;EAED,OAAOA,eAAe,CAACiE,WAAW,CAAC,CAACtK,MAAM,EAAEwG,KAAK,EAAE+D,KAAK,KAAI;AAC1D;AACA,IAAA,IAAIvD,KAAU,CAAA;IACd,IAAIwD,2BAA2B,GAAG,KAAK,CAAA;IACvC,IAAIvB,YAAY,GAA2B,IAAI,CAAA;IAC/C,IAAIiB,sBAAsB,GAA2B,IAAI,CAAA;AACzD,IAAA,IAAIvF,eAAe,EAAE;AACnBqC,MAAAA,KAAK,GAAGqC,MAAM,IAAI7C,KAAK,CAACtB,KAAK,CAACkE,EAAE,GAAGC,MAAM,CAAC7C,KAAK,CAACtB,KAAK,CAACkE,EAAE,CAAC,GAAGlD,SAAS,CAAA;AACrE+C,MAAAA,YAAY,GAAGzC,KAAK,CAACtB,KAAK,CAAC+D,YAAY,IAAIf,mBAAmB,CAAA;AAE9D,MAAA,IAAI4B,cAAc,EAAE;AAClB,QAAA,IAAIC,aAAa,GAAG,CAAC,IAAIQ,KAAK,KAAK,CAAC,EAAE;AACpCnF,UAAAA,WAAW,CACT,gBAAgB,EAChB,KAAK,EACL,0EAA0E,CAC3E,CAAA;AACDoF,UAAAA,2BAA2B,GAAG,IAAI,CAAA;AAClCN,UAAAA,sBAAsB,GAAG,IAAI,CAAA;AAC9B,SAAA,MAAM,IAAIH,aAAa,KAAKQ,KAAK,EAAE;AAClCC,UAAAA,2BAA2B,GAAG,IAAI,CAAA;AAClCN,UAAAA,sBAAsB,GAAG1D,KAAK,CAACtB,KAAK,CAACgF,sBAAsB,IAAI,IAAI,CAAA;AACpE,SAAA;AACF,OAAA;AACF,KAAA;AAED,IAAA,IAAIjK,OAAO,GAAG2E,aAAa,CAAC6F,MAAM,CAACpE,eAAe,CAACP,KAAK,CAAC,CAAC,EAAEyE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;IACvE,IAAIG,WAAW,GAAGA,MAAK;AACrB,MAAA,IAAI7B,QAAyB,CAAA;AAC7B,MAAA,IAAI7B,KAAK,EAAE;AACT6B,QAAAA,QAAQ,GAAGI,YAAY,CAAA;OACxB,MAAM,IAAIuB,2BAA2B,EAAE;AACtC3B,QAAAA,QAAQ,GAAGqB,sBAAsB,CAAA;AAClC,OAAA,MAAM,IAAI1D,KAAK,CAACtB,KAAK,CAACiB,SAAS,EAAE;AAChC;AACA;AACA;AACA;AACA;AACA;AACA0C,QAAAA,QAAQ,gBAAGrJ,oBAACgH,KAAK,CAACtB,KAAK,CAACiB,SAAS,OAAG,CAAA;AACrC,OAAA,MAAM,IAAIK,KAAK,CAACtB,KAAK,CAACe,OAAO,EAAE;AAC9B4C,QAAAA,QAAQ,GAAGrC,KAAK,CAACtB,KAAK,CAACe,OAAO,CAAA;AAC/B,OAAA,MAAM;AACL4C,QAAAA,QAAQ,GAAG7I,MAAM,CAAA;AAClB,OAAA;AACD,MAAA,oBACER,oBAACuJ,aAAa,EAAA;AACZvC,QAAAA,KAAK,EAAEA,KAAK;AACZoC,QAAAA,YAAY,EAAE;UACZ5I,MAAM;UACNC,OAAO;UACPC,WAAW,EAAEyE,eAAe,IAAI,IAAA;SACjC;AACDkE,QAAAA,QAAQ,EAAEA,QAAAA;AAAQ,OAAA,CAClB,CAAA;KAEL,CAAA;AACD;AACA;AACA;IACA,OAAOlE,eAAe,KACnB6B,KAAK,CAACtB,KAAK,CAACgE,aAAa,IAAI1C,KAAK,CAACtB,KAAK,CAAC+D,YAAY,IAAIsB,KAAK,KAAK,CAAC,CAAC,gBACtE/K,KAAC,CAAAwE,aAAA,CAAAmE,mBAAmB;MAClB/G,QAAQ,EAAEuD,eAAe,CAACvD,QAAQ;MAClCkH,YAAY,EAAE3D,eAAe,CAAC2D,YAAY;AAC1CQ,MAAAA,SAAS,EAAEG,YAAY;AACvBjC,MAAAA,KAAK,EAAEA,KAAK;MACZ6B,QAAQ,EAAE6B,WAAW,EAAE;AACvB9B,MAAAA,YAAY,EAAE;AAAE5I,QAAAA,MAAM,EAAE,IAAI;QAAEC,OAAO;AAAEC,QAAAA,WAAW,EAAE,IAAA;AAAM,OAAA;AAAA,KAAA,CAC1D,GAEFwK,WAAW,EACZ,CAAA;GACF,EAAE,IAAiC,CAAC,CAAA;AACvC,CAAA;AAEA,IAAKC,cAIJ,CAAA;AAJD,CAAA,UAAKA,cAAc,EAAA;AACjBA,EAAAA,cAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzBA,EAAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjCA,EAAAA,cAAA,CAAA,mBAAA,CAAA,GAAA,aAAiC,CAAA;AACnC,CAAC,EAJIA,cAAc,KAAdA,cAAc,GAIlB,EAAA,CAAA,CAAA,CAAA;AAED,IAAKC,mBAWJ,CAAA;AAXD,CAAA,UAAKA,mBAAmB,EAAA;AACtBA,EAAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzBA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/BA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/BA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/BA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/BA,EAAAA,mBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AACzCA,EAAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzBA,EAAAA,mBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjCA,EAAAA,mBAAA,CAAA,mBAAA,CAAA,GAAA,aAAiC,CAAA;AACjCA,EAAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AAC3B,CAAC,EAXIA,mBAAmB,KAAnBA,mBAAmB,GAWvB,EAAA,CAAA,CAAA,CAAA;AAED,SAASC,yBAAyBA,CAChCC,QAA8C,EAAA;EAE9C,OAAO,CAAA,EAAGA,QAAQ,CAA+F,6FAAA,CAAA,CAAA;AACnH,CAAA;AAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAA;AACpD,EAAA,IAAIE,GAAG,GAAGxL,KAAK,CAACmB,UAAU,CAACpB,iBAAiB,CAAC,CAAA;AAC7C,EAAA,CAAUyL,GAAG,GAAbxK,gBAAS,QAAMqK,yBAAyB,CAACC,QAAQ,CAAC,EAAzC,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAOE,GAAG,CAAA;AACZ,CAAA;AAEA,SAASC,kBAAkBA,CAACH,QAA6B,EAAA;AACvD,EAAA,IAAInH,KAAK,GAAGnE,KAAK,CAACmB,UAAU,CAAChB,sBAAsB,CAAC,CAAA;AACpD,EAAA,CAAUgE,KAAK,GAAfnD,gBAAS,QAAQqK,yBAAyB,CAACC,QAAQ,CAAC,EAA3C,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAOnH,KAAK,CAAA;AACd,CAAA;AAEA,SAASuH,eAAeA,CAACJ,QAA6B,EAAA;AACpD,EAAA,IAAI5F,KAAK,GAAG1F,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;AAC1C,EAAA,CAAUmF,KAAK,GAAf1E,gBAAS,QAAQqK,yBAAyB,CAACC,QAAQ,CAAC,EAA3C,GAAA,KAAA,CAAA,CAAA;AACT,EAAA,OAAO5F,KAAK,CAAA;AACd,CAAA;AAEA;AACA,SAASiG,iBAAiBA,CAACL,QAA6B,EAAA;AACtD,EAAA,IAAI5F,KAAK,GAAGgG,eAAe,CAACJ,QAAQ,CAAC,CAAA;AACrC,EAAA,IAAIM,SAAS,GAAGlG,KAAK,CAACjF,OAAO,CAACiF,KAAK,CAACjF,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAAA;AACvD,EAAA,CACE+G,SAAS,CAAClG,KAAK,CAACkE,EAAE,GADpB5I,gBAAS,CAAA,KAAA,EAEJ,GAAAsK,QAAgE,CAAA,sDAAA,CAAA,EAF5D,GAAA,KAAA,CAAA,CAAA;AAIT,EAAA,OAAOM,SAAS,CAAClG,KAAK,CAACkE,EAAE,CAAA;AAC3B,CAAA;AAEA;;AAEG;SACaiC,UAAUA,GAAA;AACxB,EAAA,OAAOF,iBAAiB,CAACP,mBAAmB,CAACU,UAAU,CAAC,CAAA;AAC1D,CAAA;AAEA;;;AAGG;SACaC,aAAaA,GAAA;AAC3B,EAAA,IAAI5H,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAACY,aAAa,CAAC,CAAA;EACjE,OAAO7H,KAAK,CAAC8H,UAAU,CAAA;AACzB,CAAA;AAEA;;;AAGG;SACaC,cAAcA,GAAA;AAC5B,EAAA,IAAIrJ,iBAAiB,GAAG0I,oBAAoB,CAACJ,cAAc,CAACgB,cAAc,CAAC,CAAA;AAC3E,EAAA,IAAIhI,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAACe,cAAc,CAAC,CAAA;AAClE,EAAA,OAAOnM,KAAK,CAACiC,OAAO,CAClB,OAAO;AACLmK,IAAAA,UAAU,EAAEvJ,iBAAiB,CAACwJ,MAAM,CAACD,UAAU;IAC/CjI,KAAK,EAAEA,KAAK,CAAC2E,YAAAA;AACd,GAAA,CAAC,EACF,CAACjG,iBAAiB,CAACwJ,MAAM,CAACD,UAAU,EAAEjI,KAAK,CAAC2E,YAAY,CAAC,CAC1D,CAAA;AACH,CAAA;AAEA;;;AAGG;SACawD,UAAUA,GAAA;EACxB,IAAI;IAAE7L,OAAO;AAAEkK,IAAAA,UAAAA;AAAY,GAAA,GAAGc,kBAAkB,CAC9CL,mBAAmB,CAACmB,UAAU,CAC/B,CAAA;EACD,OAAOvM,KAAK,CAACiC,OAAO,CAClB,MAAMxB,OAAO,CAACsG,GAAG,CAAEmD,CAAC,IAAKsC,iCAA0B,CAACtC,CAAC,EAAES,UAAU,CAAC,CAAC,EACnE,CAAClK,OAAO,EAAEkK,UAAU,CAAC,CACtB,CAAA;AACH,CAAA;AAEA;;AAEG;SACa8B,aAAaA,GAAA;AAC3B,EAAA,IAAItI,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAACsB,aAAa,CAAC,CAAA;AACjE,EAAA,IAAIC,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC,CAAA;AAElE,EAAA,IAAIvI,KAAK,CAAC0F,MAAM,IAAI1F,KAAK,CAAC0F,MAAM,CAAC8C,OAAO,CAAC,IAAI,IAAI,EAAE;AACjDrE,IAAAA,OAAO,CAACd,KAAK,EACkDmF,0DAAAA,EAAAA,OAAO,GAAG,CACxE,CAAA;AACD,IAAA,OAAOjG,SAAS,CAAA;AACjB,GAAA;AACD,EAAA,OAAOvC,KAAK,CAACwG,UAAU,CAACgC,OAAO,CAAC,CAAA;AAClC,CAAA;AAEA;;AAEG;AACG,SAAUC,kBAAkBA,CAACD,OAAe,EAAA;AAChD,EAAA,IAAIxI,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAACyB,kBAAkB,CAAC,CAAA;AACtE,EAAA,OAAO1I,KAAK,CAACwG,UAAU,CAACgC,OAAO,CAAC,CAAA;AAClC,CAAA;AAEA;;AAEG;SACaG,aAAaA,GAAA;AAC3B,EAAA,IAAI3I,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAAC2B,aAAa,CAAC,CAAA;AACjE,EAAA,IAAIJ,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC,CAAA;EAClE,OAAOvI,KAAK,CAAC6I,UAAU,GAAG7I,KAAK,CAAC6I,UAAU,CAACL,OAAO,CAAC,GAAGjG,SAAS,CAAA;AACjE,CAAA;AAEA;;;;AAIG;SACae,aAAaA,GAAA;AAC3B,EAAA,IAAID,KAAK,GAAGxH,KAAK,CAACmB,UAAU,CAACR,iBAAiB,CAAC,CAAA;AAC/C,EAAA,IAAIwD,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAAC6B,aAAa,CAAC,CAAA;AACjE,EAAA,IAAIN,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAAC6B,aAAa,CAAC,CAAA;AAElE;AACA;EACA,IAAIzF,KAAK,KAAKd,SAAS,EAAE;AACvB,IAAA,OAAOc,KAAK,CAAA;AACb,GAAA;AAED;AACA,EAAA,OAAOrD,KAAK,CAAC0F,MAAM,GAAG8C,OAAO,CAAC,CAAA;AAChC,CAAA;AAEA;;AAEG;SACaO,aAAaA,GAAA;AAC3B,EAAA,IAAIxI,KAAK,GAAG1E,KAAK,CAACmB,UAAU,CAACf,YAAY,CAAC,CAAA;EAC1C,OAAOsE,KAAK,EAAEyI,KAAK,CAAA;AACrB,CAAA;AAEA;;AAEG;SACaC,aAAaA,GAAA;AAC3B,EAAA,IAAI1I,KAAK,GAAG1E,KAAK,CAACmB,UAAU,CAACf,YAAY,CAAC,CAAA;EAC1C,OAAOsE,KAAK,EAAE2I,MAAM,CAAA;AACtB,CAAA;AAEA,IAAIC,SAAS,GAAG,CAAC,CAAA;AAEjB;;;;;AAKG;AACG,SAAUC,UAAUA,CAACC,WAAsC,EAAA;EAC/D,IAAI;IAAEnB,MAAM;AAAEpL,IAAAA,QAAAA;AAAU,GAAA,GAAGsK,oBAAoB,CAACJ,cAAc,CAACsC,UAAU,CAAC,CAAA;AAC1E,EAAA,IAAItJ,KAAK,GAAGsH,kBAAkB,CAACL,mBAAmB,CAACqC,UAAU,CAAC,CAAA;EAE9D,IAAI,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3N,KAAK,CAAC4N,QAAQ,CAAC,EAAE,CAAC,CAAA;AACpD,EAAA,IAAIC,eAAe,GAAG7N,KAAK,CAACyD,WAAW,CACpCqK,GAAG,IAAI;AACN,IAAA,IAAI,OAAON,WAAW,KAAK,UAAU,EAAE;MACrC,OAAO,CAAC,CAACA,WAAW,CAAA;AACrB,KAAA;IACD,IAAIvM,QAAQ,KAAK,GAAG,EAAE;MACpB,OAAOuM,WAAW,CAACM,GAAG,CAAC,CAAA;AACxB,KAAA;AAED;AACA;AACA;IACA,IAAI;MAAEC,eAAe;MAAEC,YAAY;AAAEC,MAAAA,aAAAA;AAAa,KAAE,GAAGH,GAAG,CAAA;AAC1D,IAAA,OAAON,WAAW,CAAC;AACjBO,MAAAA,eAAe,EAAE;AACf,QAAA,GAAGA,eAAe;QAClB1M,QAAQ,EACN6M,aAAa,CAACH,eAAe,CAAC1M,QAAQ,EAAEJ,QAAQ,CAAC,IACjD8M,eAAe,CAAC1M,QAAAA;OACnB;AACD2M,MAAAA,YAAY,EAAE;AACZ,QAAA,GAAGA,YAAY;QACf3M,QAAQ,EACN6M,aAAa,CAACF,YAAY,CAAC3M,QAAQ,EAAEJ,QAAQ,CAAC,IAC9C+M,YAAY,CAAC3M,QAAAA;OAChB;AACD4M,MAAAA,aAAAA;AACD,KAAA,CAAC,CAAA;AACJ,GAAC,EACD,CAAChN,QAAQ,EAAEuM,WAAW,CAAC,CACxB,CAAA;AAED;AACA;EACAxN,KAAK,CAACmO,SAAS,CAAC,MAAK;AACnB,IAAA,IAAI/G,GAAG,GAAGgH,MAAM,CAAC,EAAEd,SAAS,CAAC,CAAA;IAC7BK,aAAa,CAACvG,GAAG,CAAC,CAAA;AAClB,IAAA,OAAO,MAAMiF,MAAM,CAACgC,aAAa,CAACjH,GAAG,CAAC,CAAA;AACxC,GAAC,EAAE,CAACiF,MAAM,CAAC,CAAC,CAAA;AAEZ;AACA;AACA;AACA;EACArM,KAAK,CAACmO,SAAS,CAAC,MAAK;IACnB,IAAIT,UAAU,KAAK,EAAE,EAAE;AACrBrB,MAAAA,MAAM,CAACiC,UAAU,CAACZ,UAAU,EAAEG,eAAe,CAAC,CAAA;AAC/C,KAAA;GACF,EAAE,CAACxB,MAAM,EAAEqB,UAAU,EAAEG,eAAe,CAAC,CAAC,CAAA;AAEzC;AACA;EACA,OAAOH,UAAU,IAAIvJ,KAAK,CAACoK,QAAQ,CAACC,GAAG,CAACd,UAAU,CAAC,GAC/CvJ,KAAK,CAACoK,QAAQ,CAACE,GAAG,CAACf,UAAU,CAAE,GAC/BgB,YAAY,CAAA;AAClB,CAAA;AAEA;;;AAGG;AACH,SAAS/L,iBAAiBA,GAAA;EACxB,IAAI;AAAE0J,IAAAA,MAAAA;AAAM,GAAE,GAAGd,oBAAoB,CAACJ,cAAc,CAACwD,iBAAiB,CAAC,CAAA;AACvE,EAAA,IAAI/E,EAAE,GAAG+B,iBAAiB,CAACP,mBAAmB,CAACuD,iBAAiB,CAAC,CAAA;AAEjE,EAAA,IAAItL,SAAS,GAAGrD,KAAK,CAACsD,MAAM,CAAC,KAAK,CAAC,CAAA;AACnCjB,EAAAA,yBAAyB,CAAC,MAAK;IAC7BgB,SAAS,CAACE,OAAO,GAAG,IAAI,CAAA;AAC1B,GAAC,CAAC,CAAA;AAEF,EAAA,IAAIC,QAAQ,GAAqBxD,KAAK,CAACyD,WAAW,CAChD,CAAC5C,EAAe,EAAE6C,OAAA,GAA2B,EAAE,KAAI;AACjDC,IAAAC,cAAO,CAACP,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC,CAAA,CAAA;AAEjD;AACA;AACA,IAAA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE,OAAA;AAExB,IAAA,IAAI,OAAO1C,EAAE,KAAK,QAAQ,EAAE;AAC1BwL,MAAAA,MAAM,CAAC7I,QAAQ,CAAC3C,EAAE,CAAC,CAAA;AACpB,KAAA,MAAM;AACLwL,MAAAA,MAAM,CAAC7I,QAAQ,CAAC3C,EAAE,EAAE;AAAE+N,QAAAA,WAAW,EAAEhF,EAAE;QAAE,GAAGlG,OAAAA;AAAO,OAAE,CAAC,CAAA;AACrD,KAAA;AACH,GAAC,EACD,CAAC2I,MAAM,EAAEzC,EAAE,CAAC,CACb,CAAA;AAED,EAAA,OAAOpG,QAAQ,CAAA;AACjB,CAAA;AAEA,MAAMqL,eAAa,GAA4B,EAAE,CAAA;AAEjD,SAASjJ,WAAWA,CAACwB,GAAW,EAAE0H,IAAa,EAAEpH,OAAe,EAAA;EAC9D,IAAI,CAACoH,IAAI,IAAI,CAACD,eAAa,CAACzH,GAAG,CAAC,EAAE;AAChCyH,IAAAA,eAAa,CAACzH,GAAG,CAAC,GAAG,IAAI,CAAA;IACzBxD,cAAO,CAAC,KAAK,EAAE8D,OAAO,CAAC,CAAA,CAAA;AACxB,GAAA;AACH;;AC9lCA,MAAMmH,aAAa,GAA+B,EAAE,CAAA;AAEpC,SAAAE,QAAQA,CAAC3H,GAAW,EAAEM,OAAe,EAAA;EACnD,IAAe,CAACmH,aAAa,CAACnH,OAAO,CAAC,EAAE;AACtCmH,IAAAA,aAAa,CAACnH,OAAO,CAAC,GAAG,IAAI,CAAA;AAC7BY,IAAAA,OAAO,CAAC0G,IAAI,CAACtH,OAAO,CAAC,CAAA;AACtB,GAAA;AACH,CAAA;AAEA,MAAMuH,cAAc,GAAGA,CAACC,IAAY,EAAEC,GAAW,EAAEC,IAAY,KAC7DL,QAAQ,CACNG,IAAI,EACJ,CAAwCC,qCAAAA,EAAAA,GAAO,CAAA,EAAA,CAAA,GAC7C,CAAqBD,kBAAAA,EAAAA,IAAsC,kCAAA,GAC9B,CAAA,0BAAA,EAAAE,IAAO,CAAA,CAAA,CAAA,CACvC,CAAA;AAEa,SAAAC,wBAAwBA,CACtCC,YAAqD,EACrDC,YAA6D,EAAA;AAE7D,EAAA,IAAID,YAAY,EAAEE,kBAAkB,KAAK9I,SAAS,EAAE;AAClDuI,IAAAA,cAAc,CACZ,oBAAoB,EACpB,iFAAiF,EACjF,gEAAgE,CACjE,CAAA;AACF,GAAA;AAED,EAAA,IACEK,YAAY,EAAElM,oBAAoB,KAAKsD,SAAS,KAC/C,CAAC6I,YAAY,IAAIA,YAAY,CAACnM,oBAAoB,KAAKsD,SAAS,CAAC,EAClE;AACAuI,IAAAA,cAAc,CACZ,sBAAsB,EACtB,iEAAiE,EACjE,kEAAkE,CACnE,CAAA;AACF,GAAA;AAED,EAAA,IAAIM,YAAY,EAAE;AAChB,IAAA,IAAIA,YAAY,CAACE,iBAAiB,KAAK/I,SAAS,EAAE;AAChDuI,MAAAA,cAAc,CACZ,mBAAmB,EACnB,wDAAwD,EACxD,+DAA+D,CAChE,CAAA;AACF,KAAA;AAED,IAAA,IAAIM,YAAY,CAACG,sBAAsB,KAAKhJ,SAAS,EAAE;AACrDuI,MAAAA,cAAc,CACZ,wBAAwB,EACxB,sEAAsE,EACtE,oEAAoE,CACrE,CAAA;AACF,KAAA;AAED,IAAA,IAAIM,YAAY,CAACzF,mBAAmB,KAAKpD,SAAS,EAAE;AAClDuI,MAAAA,cAAc,CACZ,qBAAqB,EACrB,uDAAuD,EACvD,iEAAiE,CAClE,CAAA;AACF,KAAA;AAED,IAAA,IAAIM,YAAY,CAACI,8BAA8B,KAAKjJ,SAAS,EAAE;AAC7DuI,MAAAA,cAAc,CACZ,gCAAgC,EAChC,8EAA8E,EAC9E,4EAA4E,CAC7E,CAAA;AACF,KAAA;AACF,GAAA;AACH;;ACVA;;;;;;;;;;;;;;;;;;;;AAoBE;AACF,MAAMW,gBAAgB,GAAG,iBAAiB,CAAA;AAC1C,MAAMC,mBAAmB,GAAG7P,KAAK,CAAC4P,gBAAgB,CAAC,CAAA;AAEnD;;AAEG;AACG,SAAUE,cAAcA,CAAC;EAC7BC,eAAe;EACf1D,MAAM;AACNvJ,EAAAA,MAAAA;AACoB,CAAA,EAAA;AACpB,EAAA,IAAI,CAACqB,KAAK,EAAE6L,YAAY,CAAC,GAAGhQ,KAAK,CAAC4N,QAAQ,CAACvB,MAAM,CAAClI,KAAK,CAAC,CAAA;EACxD,IAAI;AAAEqL,IAAAA,kBAAAA;AAAkB,GAAE,GAAG1M,MAAM,IAAI,EAAE,CAAA;AAEzC,EAAA,IAAImN,QAAQ,GAAGjQ,KAAK,CAACyD,WAAW,CAC7ByM,QAAqB,IAAI;IACxB,IAAIV,kBAAkB,IAAIK,mBAAmB,EAAE;AAC7CA,MAAAA,mBAAmB,CAAC,MAAMG,YAAY,CAACE,QAAQ,CAAC,CAAC,CAAA;AAClD,KAAA,MAAM;MACLF,YAAY,CAACE,QAAQ,CAAC,CAAA;AACvB,KAAA;AACH,GAAC,EACD,CAACF,YAAY,EAAER,kBAAkB,CAAC,CACnC,CAAA;AAED;AACA;AACAxP,EAAAA,KAAK,CAACyC,eAAe,CAAC,MAAM4J,MAAM,CAAC8D,SAAS,CAACF,QAAQ,CAAC,EAAE,CAAC5D,MAAM,EAAE4D,QAAQ,CAAC,CAAC,CAAA;EAE3EjQ,KAAK,CAACmO,SAAS,CAAC,MAAK;IACnBvK,cAAO,CACLmM,eAAe,IAAI,IAAI,IAAI,CAAC1D,MAAM,CAACvJ,MAAM,CAACgH,mBAAmB,EAC7D,8DAA8D,GAC5D,kEAAkE,CACrE,CAAA,CAAA;AACD;AACA;GACD,EAAE,EAAE,CAAC,CAAA;AAEN,EAAA,IAAI5I,SAAS,GAAGlB,KAAK,CAACiC,OAAO,CAAC,MAAgB;IAC5C,OAAO;MACLP,UAAU,EAAE2K,MAAM,CAAC3K,UAAU;MAC7ByF,cAAc,EAAEkF,MAAM,CAAClF,cAAc;MACrCtD,EAAE,EAAGuM,CAAC,IAAK/D,MAAM,CAAC7I,QAAQ,CAAC4M,CAAC,CAAC;AAC7BlM,MAAAA,IAAI,EAAEA,CAACrD,EAAE,EAAEsD,KAAK,EAAEkM,IAAI,KACpBhE,MAAM,CAAC7I,QAAQ,CAAC3C,EAAE,EAAE;QAClBsD,KAAK;QACLmM,kBAAkB,EAAED,IAAI,EAAEC,kBAAAA;OAC3B,CAAC;AACJrM,MAAAA,OAAO,EAAEA,CAACpD,EAAE,EAAEsD,KAAK,EAAEkM,IAAI,KACvBhE,MAAM,CAAC7I,QAAQ,CAAC3C,EAAE,EAAE;AAClBoD,QAAAA,OAAO,EAAE,IAAI;QACbE,KAAK;QACLmM,kBAAkB,EAAED,IAAI,EAAEC,kBAAAA;OAC3B,CAAA;KACJ,CAAA;AACH,GAAC,EAAE,CAACjE,MAAM,CAAC,CAAC,CAAA;AAEZ,EAAA,IAAIpL,QAAQ,GAAGoL,MAAM,CAACpL,QAAQ,IAAI,GAAG,CAAA;AAErC,EAAA,IAAI4B,iBAAiB,GAAG7C,KAAK,CAACiC,OAAO,CACnC,OAAO;IACLoK,MAAM;IACNnL,SAAS;AACTsB,IAAAA,MAAM,EAAE,KAAK;AACbvB,IAAAA,QAAAA;GACD,CAAC,EACF,CAACoL,MAAM,EAAEnL,SAAS,EAAED,QAAQ,CAAC,CAC9B,CAAA;AAEDjB,EAAAA,KAAK,CAACmO,SAAS,CACb,MAAMkB,wBAAwB,CAACvM,MAAM,EAAEuJ,MAAM,CAACvJ,MAAM,CAAC,EACrD,CAACuJ,MAAM,EAAEvJ,MAAM,CAAC,CACjB,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,oBACE9C,KAAA,CAAAwE,aAAA,CAAAxE,KAAA,CAAAuI,QAAA,EAAA,IAAA,eACEvI,KAAA,CAAAwE,aAAA,CAACzE,iBAAiB,CAAC0E,QAAQ,EAAC;AAAAC,IAAAA,KAAK,EAAE7B,iBAAAA;GAAiB,eAClD7C,KAAA,CAAAwE,aAAA,CAACrE,sBAAsB,CAACsE,QAAQ,EAAC;AAAAC,IAAAA,KAAK,EAAEP,KAAAA;AAAK,GAAA,eAC3CnE,KAAC,CAAAwE,aAAA,CAAA+L,MAAM;AACLtP,IAAAA,QAAQ,EAAEA,QAAQ;IAClBW,QAAQ,EAAEuC,KAAK,CAACvC,QAAQ;IACxBE,cAAc,EAAEqC,KAAK,CAAC8J,aAAa;AACnC/M,IAAAA,SAAS,EAAEA,SAAS;AACpB4B,IAAAA,MAAM,EAAE;AACNM,MAAAA,oBAAoB,EAAEiJ,MAAM,CAACvJ,MAAM,CAACM,oBAAAA;AACrC,KAAA;AAAA,GAAA,EAEAe,KAAK,CAAC4F,WAAW,IAAIsC,MAAM,CAACvJ,MAAM,CAACgH,mBAAmB,gBACrD9J,KAAA,CAAAwE,aAAA,CAACgM,UAAU,EACT;IAAAxL,MAAM,EAAEqH,MAAM,CAACrH,MAAM;IACrBlC,MAAM,EAAEuJ,MAAM,CAACvJ,MAAM;AACrBqB,IAAAA,KAAK,EAAEA,KAAAA;IACP,GAEF4L,eACD,CACM,CACuB,CACP,EAC5B,IAAI,CACJ,CAAA;AAEP,CAAA;AAEA,SAASS,UAAUA,CAAC;EAClBxL,MAAM;EACNlC,MAAM;AACNqB,EAAAA,KAAAA;AAKD,CAAA,EAAA;EACC,OAAOe,aAAa,CAACF,MAAM,EAAE0B,SAAS,EAAEvC,KAAK,EAAErB,MAAM,CAAC,CAAA;AACxD,CAAA;AAUA;;;;AAIG;AACa,SAAA2N,YAAYA,CAAC;EAC3BxP,QAAQ;EACRoI,QAAQ;EACRqH,cAAc;EACdC,YAAY;AACZ7N,EAAAA,MAAAA;AACkB,CAAA,EAAA;AAClB,EAAA,IAAI8N,UAAU,GAAG5Q,KAAK,CAACsD,MAAM,EAAiB,CAAA;AAC9C,EAAA,IAAIsN,UAAU,CAACrN,OAAO,IAAI,IAAI,EAAE;AAC9BqN,IAAAA,UAAU,CAACrN,OAAO,GAAGsN,mBAAmB,CAAC;MACvCH,cAAc;MACdC,YAAY;AACZG,MAAAA,QAAQ,EAAE,IAAA;AACX,KAAA,CAAC,CAAA;AACH,GAAA;AAED,EAAA,IAAIC,OAAO,GAAGH,UAAU,CAACrN,OAAO,CAAA;EAChC,IAAI,CAACY,KAAK,EAAE6L,YAAY,CAAC,GAAGhQ,KAAK,CAAC4N,QAAQ,CAAC;IACzCoD,MAAM,EAAED,OAAO,CAACC,MAAM;IACtBpP,QAAQ,EAAEmP,OAAO,CAACnP,QAAAA;AACnB,GAAA,CAAC,CAAA;EACF,IAAI;AAAE4N,IAAAA,kBAAAA;AAAkB,GAAE,GAAG1M,MAAM,IAAI,EAAE,CAAA;AACzC,EAAA,IAAImN,QAAQ,GAAGjQ,KAAK,CAACyD,WAAW,CAC7ByM,QAAwD,IAAI;AAC3DV,IAAAA,kBAAkB,IAAIK,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMG,YAAY,CAACE,QAAQ,CAAC,CAAC,GACjDF,YAAY,CAACE,QAAQ,CAAC,CAAA;AAC5B,GAAC,EACD,CAACF,YAAY,EAAER,kBAAkB,CAAC,CACnC,CAAA;AAEDxP,EAAAA,KAAK,CAACyC,eAAe,CAAC,MAAMsO,OAAO,CAACE,MAAM,CAAChB,QAAQ,CAAC,EAAE,CAACc,OAAO,EAAEd,QAAQ,CAAC,CAAC,CAAA;AAE1EjQ,EAAAA,KAAK,CAACmO,SAAS,CAAC,MAAMkB,wBAAwB,CAACvM,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;AAEjE,EAAA,oBACE9C,KAAA,CAAAwE,aAAA,CAAC+L,MAAM,EAAA;AACLtP,IAAAA,QAAQ,EAAEA,QAAQ;AAClBoI,IAAAA,QAAQ,EAAEA,QAAQ;IAClBzH,QAAQ,EAAEuC,KAAK,CAACvC,QAAQ;IACxBE,cAAc,EAAEqC,KAAK,CAAC6M,MAAM;AAC5B9P,IAAAA,SAAS,EAAE6P,OAAO;AAClBjO,IAAAA,MAAM,EAAEA,MAAAA;AAAM,GAAA,CACd,CAAA;AAEN,CAAA;AASA;;;;;;;;AAQG;AACG,SAAUoO,QAAQA,CAAC;EACvBrQ,EAAE;EACFoD,OAAO;EACPE,KAAK;AACLrD,EAAAA,QAAAA;AACc,CAAA,EAAA;EACd,CACEC,kBAAkB,EAAE,GADtBC,gBAAS,CAAA,KAAA;AAEP;AACA;AACA,EAAA,CAAA,mEAAA,CAAqE,EAJ9D,GAAA,KAAA,CAAA,CAAA;EAOT,IAAI;IAAE8B,MAAM;AAAEN,IAAAA,MAAM,EAAED,QAAAA;AAAQ,GAAE,GAAGvC,KAAK,CAACmB,UAAU,CAACd,iBAAiB,CAAC,CAAA;AAEtEsD,EAAAC,cAAO,CACL,CAACrB,QAAQ,EACgE,CAAA,uEAAA,CAAA,GACC,CAAA,sEAAA,CAAA,GACxE,0EAA0E,CAC7E,CAAA,CAAA;EAED,IAAI;AAAE9B,IAAAA,OAAAA;AAAO,GAAE,GAAGT,KAAK,CAACmB,UAAU,CAACZ,YAAY,CAAC,CAAA;EAChD,IAAI;AAAEc,IAAAA,QAAQ,EAAE0B,gBAAAA;GAAkB,GAAGpB,WAAW,EAAE,CAAA;AAClD,EAAA,IAAI6B,QAAQ,GAAGd,WAAW,EAAE,CAAA;AAE5B;AACA;EACA,IAAIoB,IAAI,GAAGC,SAAS,CAClBlD,EAAE,EACFsC,0BAAmB,CAAC1C,OAAO,EAAEqC,MAAM,CAACM,oBAAoB,CAAC,EACzDL,gBAAgB,EAChBjC,QAAQ,KAAK,MAAM,CACpB,CAAA;AACD,EAAA,IAAIqQ,QAAQ,GAAGlO,IAAI,CAACC,SAAS,CAACY,IAAI,CAAC,CAAA;AAEnC9D,EAAAA,KAAK,CAACmO,SAAS,CACb,MAAM3K,QAAQ,CAACP,IAAI,CAACe,KAAK,CAACmN,QAAQ,CAAC,EAAE;IAAElN,OAAO;IAAEE,KAAK;AAAErD,IAAAA,QAAAA;AAAU,GAAA,CAAC,EAClE,CAAC0C,QAAQ,EAAE2N,QAAQ,EAAErQ,QAAQ,EAAEmD,OAAO,EAAEE,KAAK,CAAC,CAC/C,CAAA;AAED,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAMA;;;;AAIG;AACG,SAAUiN,MAAMA,CAACvI,KAAkB,EAAA;AACvC,EAAA,OAAOvE,SAAS,CAACuE,KAAK,CAACtE,OAAO,CAAC,CAAA;AACjC,CAAA;AA8CA;;;;AAIG;AACG,SAAU8M,KAAKA,CAACC,MAAkB,EAAA;AACtC,EAAAtQ,gBAAS,CAE+D,KAAA,EAAA,CAAA,oEAAA,CAAA,GACpE,CAAA,gEAAA,CAAkE,EAH7D,CAAA,CAAA;AAKX,CAAA;AAYA;;;;;;;;AAQG;AACa,SAAAuP,MAAMA,CAAC;EACrBtP,QAAQ,EAAEsQ,YAAY,GAAG,GAAG;AAC5BlI,EAAAA,QAAQ,GAAG,IAAI;AACfzH,EAAAA,QAAQ,EAAE4P,YAAY;EACtB1P,cAAc,GAAGuF,MAAc,CAACC,GAAG;EACnCpG,SAAS;EACTsB,MAAM,EAAEiP,UAAU,GAAG,KAAK;AAC1B3O,EAAAA,MAAAA;AACY,CAAA,EAAA;AACZ,EAAA,CACE,CAAC/B,kBAAkB,EAAE,GADvBC,gBAAS,CAAA,KAAA,EAEgD,uDAAA,GACrD,CAAA,iDAAA,CAAmD,EAH9C,GAAA,KAAA,CAAA,CAAA;AAMT;AACA;EACA,IAAIC,QAAQ,GAAGsQ,YAAY,CAACtN,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;AAChD,EAAA,IAAIyN,iBAAiB,GAAG1R,KAAK,CAACiC,OAAO,CACnC,OAAO;IACLhB,QAAQ;IACRC,SAAS;AACTsB,IAAAA,MAAM,EAAEiP,UAAU;AAClB3O,IAAAA,MAAM,EAAE;AACNM,MAAAA,oBAAoB,EAAE,KAAK;MAC3B,GAAGN,MAAAA;AACJ,KAAA;GACF,CAAC,EACF,CAAC7B,QAAQ,EAAE6B,MAAM,EAAE5B,SAAS,EAAEuQ,UAAU,CAAC,CAC1C,CAAA;AAED,EAAA,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;AACpCA,IAAAA,YAAY,GAAGxL,SAAS,CAACwL,YAAY,CAAC,CAAA;AACvC,GAAA;EAED,IAAI;AACFnQ,IAAAA,QAAQ,GAAG,GAAG;AACdC,IAAAA,MAAM,GAAG,EAAE;AACXF,IAAAA,IAAI,GAAG,EAAE;AACT+C,IAAAA,KAAK,GAAG,IAAI;AACZiD,IAAAA,GAAG,GAAG,SAAA;AAAS,GAChB,GAAGoK,YAAY,CAAA;AAEhB,EAAA,IAAIG,eAAe,GAAG3R,KAAK,CAACiC,OAAO,CAAC,MAAK;AACvC,IAAA,IAAI2P,gBAAgB,GAAG1D,aAAa,CAAC7M,QAAQ,EAAEJ,QAAQ,CAAC,CAAA;IAExD,IAAI2Q,gBAAgB,IAAI,IAAI,EAAE;AAC5B,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;IAED,OAAO;AACLhQ,MAAAA,QAAQ,EAAE;AACRP,QAAAA,QAAQ,EAAEuQ,gBAAgB;QAC1BtQ,MAAM;QACNF,IAAI;QACJ+C,KAAK;AACLiD,QAAAA,GAAAA;OACD;AACDtF,MAAAA,cAAAA;KACD,CAAA;AACH,GAAC,EAAE,CAACb,QAAQ,EAAEI,QAAQ,EAAEC,MAAM,EAAEF,IAAI,EAAE+C,KAAK,EAAEiD,GAAG,EAAEtF,cAAc,CAAC,CAAC,CAAA;EAElE8B,cAAO,CACL+N,eAAe,IAAI,IAAI,EACvB,CAAA,kBAAA,EAAqB1Q,QAA0C,CAAA,gCAAA,CAAA,GAC7D,CAAII,CAAAA,EAAAA,QAAQ,CAAGC,EAAAA,MAAM,GAAGF,IAA2C,CAAA,qCAAA,CAAA,GACnE,CAAA,gDAAA,CAAkD,CACrD,CAAA,CAAA;EAED,IAAIuQ,eAAe,IAAI,IAAI,EAAE;AAC3B,IAAA,OAAO,IAAI,CAAA;AACZ,GAAA;AAED,EAAA,oBACE3R,oBAACK,iBAAiB,CAACoE,QAAQ,EAAC;AAAAC,IAAAA,KAAK,EAAEgN,iBAAAA;GAAiB,eAClD1R,KAAA,CAAAwE,aAAA,CAAClE,eAAe,CAACmE,QAAQ,EAAA;AAAC4E,IAAAA,QAAQ,EAAEA,QAAQ;AAAE3E,IAAAA,KAAK,EAAEiN,eAAAA;AAAmB,GAAA,CAAA,CAC7C,CAAA;AAEjC,CAAA;AAOA;;;;;AAKG;SACaE,MAAMA,CAAC;EACrBxI,QAAQ;AACRzH,EAAAA,QAAAA;AACY,CAAA,EAAA;EACZ,OAAOmD,SAAS,CAAC+M,wBAAwB,CAACzI,QAAQ,CAAC,EAAEzH,QAAQ,CAAC,CAAA;AAChE,CAAA;AAYA;;;AAGG;AACG,SAAUmQ,KAAKA,CAAC;EAAE1I,QAAQ;EAAEI,YAAY;AAAEuI,EAAAA,OAAAA;AAAqB,CAAA,EAAA;AACnE,EAAA,oBACEhS,KAAC,CAAAwE,aAAA,CAAAyN,kBAAkB,EAAC;AAAAD,IAAAA,OAAO,EAAEA,OAAO;AAAEvI,IAAAA,YAAY,EAAEA,YAAAA;GAAY,eAC9DzJ,KAAA,CAAAwE,aAAA,CAAC0N,YAAY,EAAE,IAAA,EAAA7I,QAAQ,CAAgB,CACpB,CAAA;AAEzB,CAAA;AAWA,IAAK8I,iBAIJ,CAAA;AAJD,CAAA,UAAKA,iBAAiB,EAAA;EACpBA,iBAAA,CAAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;EACPA,iBAAA,CAAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;EACPA,iBAAA,CAAAA,iBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACP,CAAC,EAJIA,iBAAiB,KAAjBA,iBAAiB,GAIrB,EAAA,CAAA,CAAA,CAAA;AAED,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,MAAK,EAAG,CAAC,CAAA;AAEjD,MAAMJ,kBAAmB,SAAQjS,KAAK,CAAC2G,SAGtC,CAAA;EACCiC,WAAAA,CAAYC,KAA8B,EAAA;IACxC,KAAK,CAACA,KAAK,CAAC,CAAA;IACZ,IAAI,CAAC1E,KAAK,GAAG;AAAEqD,MAAAA,KAAK,EAAE,IAAA;KAAM,CAAA;AAC9B,GAAA;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAA;IACxC,OAAO;AAAEA,MAAAA,KAAAA;KAAO,CAAA;AAClB,GAAA;AAEAyB,EAAAA,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAA;IAC1CZ,OAAO,CAACd,KAAK,CACX,kDAAkD,EAClDA,KAAK,EACL0B,SAAS,CACV,CAAA;AACH,GAAA;AAEAC,EAAAA,MAAMA,GAAA;IACJ,IAAI;MAAEE,QAAQ;MAAEI,YAAY;AAAEuI,MAAAA,OAAAA;KAAS,GAAG,IAAI,CAACnJ,KAAK,CAAA;IAEpD,IAAIyJ,OAAO,GAA0B,IAAI,CAAA;AACzC,IAAA,IAAI1K,MAAM,GAAsBuK,iBAAiB,CAACI,OAAO,CAAA;AAEzD,IAAA,IAAI,EAAEP,OAAO,YAAYK,OAAO,CAAC,EAAE;AACjC;MACAzK,MAAM,GAAGuK,iBAAiB,CAACK,OAAO,CAAA;AAClCF,MAAAA,OAAO,GAAGD,OAAO,CAACL,OAAO,EAAE,CAAA;AAC3B/K,MAAAA,MAAM,CAACwL,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAE7D,GAAG,EAAEA,MAAM,IAAA;AAAI,OAAE,CAAC,CAAA;AAC/DxH,MAAAA,MAAM,CAACwL,cAAc,CAACH,OAAO,EAAE,OAAO,EAAE;QAAE7D,GAAG,EAAEA,MAAMuD,OAAAA;AAAO,OAAE,CAAC,CAAA;AAChE,KAAA,MAAM,IAAI,IAAI,CAAC7N,KAAK,CAACqD,KAAK,EAAE;AAC3B;MACAI,MAAM,GAAGuK,iBAAiB,CAAC3K,KAAK,CAAA;AAChC,MAAA,IAAIkL,WAAW,GAAG,IAAI,CAACvO,KAAK,CAACqD,KAAK,CAAA;AAClC8K,MAAAA,OAAO,GAAGD,OAAO,CAACM,MAAM,EAAE,CAACC,KAAK,CAAC,MAAO,EAAC,CAAC,CAAC;AAC3C3L,MAAAA,MAAM,CAACwL,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAE7D,GAAG,EAAEA,MAAM,IAAA;AAAI,OAAE,CAAC,CAAA;AAC/DxH,MAAAA,MAAM,CAACwL,cAAc,CAACH,OAAO,EAAE,QAAQ,EAAE;QAAE7D,GAAG,EAAEA,MAAMiE,WAAAA;AAAW,OAAE,CAAC,CAAA;AACrE,KAAA,MAAM,IAAKV,OAA0B,CAACa,QAAQ,EAAE;AAC/C;AACAP,MAAAA,OAAO,GAAGN,OAAO,CAAA;AACjBpK,MAAAA,MAAM,GACJ,QAAQ,IAAI0K,OAAO,GACfH,iBAAiB,CAAC3K,KAAK,GACvB,OAAO,IAAI8K,OAAO,GAClBH,iBAAiB,CAACK,OAAO,GACzBL,iBAAiB,CAACI,OAAO,CAAA;AAChC,KAAA,MAAM;AACL;MACA3K,MAAM,GAAGuK,iBAAiB,CAACI,OAAO,CAAA;AAClCtL,MAAAA,MAAM,CAACwL,cAAc,CAACT,OAAO,EAAE,UAAU,EAAE;QAAEvD,GAAG,EAAEA,MAAM,IAAA;AAAI,OAAE,CAAC,CAAA;AAC/D6D,MAAAA,OAAO,GAAGN,OAAO,CAACc,IAAI,CACnBC,IAAS,IACR9L,MAAM,CAACwL,cAAc,CAACT,OAAO,EAAE,OAAO,EAAE;QAAEvD,GAAG,EAAEA,MAAMsE,IAAAA;OAAM,CAAC,EAC7DvL,KAAU,IACTP,MAAM,CAACwL,cAAc,CAACT,OAAO,EAAE,QAAQ,EAAE;QAAEvD,GAAG,EAAEA,MAAMjH,KAAAA;AAAK,OAAE,CAAC,CACjE,CAAA;AACF,KAAA;IAED,IACEI,MAAM,KAAKuK,iBAAiB,CAAC3K,KAAK,IAClC8K,OAAO,CAACjF,MAAM,YAAY2F,oBAAoB,EAC9C;AACA;AACA,MAAA,MAAMZ,mBAAmB,CAAA;AAC1B,KAAA;IAED,IAAIxK,MAAM,KAAKuK,iBAAiB,CAAC3K,KAAK,IAAI,CAACiC,YAAY,EAAE;AACvD;MACA,MAAM6I,OAAO,CAACjF,MAAM,CAAA;AACrB,KAAA;AAED,IAAA,IAAIzF,MAAM,KAAKuK,iBAAiB,CAAC3K,KAAK,EAAE;AACtC;AACA,MAAA,oBAAOxH,KAAC,CAAAwE,aAAA,CAAApE,YAAY,CAACqE,QAAQ,EAAC;AAAAC,QAAAA,KAAK,EAAE4N,OAAO;AAAEjJ,QAAAA,QAAQ,EAAEI,YAAAA;QAAgB,CAAA;AACzE,KAAA;AAED,IAAA,IAAI7B,MAAM,KAAKuK,iBAAiB,CAACK,OAAO,EAAE;AACxC;AACA,MAAA,oBAAOxS,KAAC,CAAAwE,aAAA,CAAApE,YAAY,CAACqE,QAAQ,EAAC;AAAAC,QAAAA,KAAK,EAAE4N,OAAO;AAAEjJ,QAAAA,QAAQ,EAAEA,QAAAA;QAAY,CAAA;AACrE,KAAA;AAED;AACA,IAAA,MAAMiJ,OAAO,CAAA;AACf,GAAA;AACD,CAAA;AAED;;;AAGG;AACH,SAASJ,YAAYA,CAAC;AACpB7I,EAAAA,QAAAA;AAGD,CAAA,EAAA;AACC,EAAA,IAAI0J,IAAI,GAAG7F,aAAa,EAAE,CAAA;AAC1B,EAAA,IAAI+F,QAAQ,GAAG,OAAO5J,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC0J,IAAI,CAAC,GAAG1J,QAAQ,CAAA;EACzE,oBAAOrJ,KAAA,CAAAwE,aAAA,CAAAxE,KAAA,CAAAuI,QAAA,EAAA,IAAA,EAAG0K,QAAQ,CAAI,CAAA;AACxB,CAAA;AAEA;AACA;AACA;AAEA;;;;;;AAMG;SACanB,wBAAwBA,CACtCzI,QAAyB,EACzB1D,aAAuB,EAAE,EAAA;EAEzB,IAAIX,MAAM,GAAkB,EAAE,CAAA;EAE9BhF,KAAK,CAACkT,QAAQ,CAACC,OAAO,CAAC9J,QAAQ,EAAE,CAAC5C,OAAO,EAAEsE,KAAK,KAAI;AAClD,IAAA,IAAI,eAAC/K,KAAK,CAACoT,cAAc,CAAC3M,OAAO,CAAC,EAAE;AAClC;AACA;AACA,MAAA,OAAA;AACD,KAAA;AAED,IAAA,IAAI4M,QAAQ,GAAG,CAAC,GAAG1N,UAAU,EAAEoF,KAAK,CAAC,CAAA;AAErC,IAAA,IAAItE,OAAO,CAAC6M,IAAI,KAAKtT,KAAK,CAACuI,QAAQ,EAAE;AACnC;AACAvD,MAAAA,MAAM,CAACd,IAAI,CAACqP,KAAK,CACfvO,MAAM,EACN8M,wBAAwB,CAACrL,OAAO,CAACoC,KAAK,CAACQ,QAAQ,EAAEgK,QAAQ,CAAC,CAC3D,CAAA;AACD,MAAA,OAAA;AACD,KAAA;AAED,IAAA,EACE5M,OAAO,CAAC6M,IAAI,KAAKjC,KAAK,CAAA1N,GADxB3C,gBAAS,QAEP,CACE,CAAA,EAAA,OAAOyF,OAAO,CAAC6M,IAAI,KAAK,QAAQ,GAAG7M,OAAO,CAAC6M,IAAI,GAAG7M,OAAO,CAAC6M,IAAI,CAACE,IACjE,CAAA,sGAAA,CAAwG,EAJjG,GAAA,KAAA,CAAA,CAAA;IAOT,EACE,CAAC/M,OAAO,CAACoC,KAAK,CAACkC,KAAK,IAAI,CAACtE,OAAO,CAACoC,KAAK,CAACQ,QAAQ,CAAA1F,GADjD3C,gBAAS,CAAA,KAAA,EAEP,0CAA0C,CAAA,CAFnC,GAAA,KAAA,CAAA,CAAA;AAKT,IAAA,IAAI0E,KAAK,GAAgB;AACvBkE,MAAAA,EAAE,EAAEnD,OAAO,CAACoC,KAAK,CAACe,EAAE,IAAIyJ,QAAQ,CAAC9M,IAAI,CAAC,GAAG,CAAC;AAC1CkN,MAAAA,aAAa,EAAEhN,OAAO,CAACoC,KAAK,CAAC4K,aAAa;AAC1ChN,MAAAA,OAAO,EAAEA,OAAO,CAACoC,KAAK,CAACpC,OAAO;AAC9BE,MAAAA,SAAS,EAAEF,OAAO,CAACoC,KAAK,CAAClC,SAAS;AAClCoE,MAAAA,KAAK,EAAEtE,OAAO,CAACoC,KAAK,CAACkC,KAAK;AAC1BjH,MAAAA,IAAI,EAAE2C,OAAO,CAACoC,KAAK,CAAC/E,IAAI;AACxB+G,MAAAA,MAAM,EAAEpE,OAAO,CAACoC,KAAK,CAACgC,MAAM;AAC5BmG,MAAAA,MAAM,EAAEvK,OAAO,CAACoC,KAAK,CAACmI,MAAM;AAC5BvH,MAAAA,YAAY,EAAEhD,OAAO,CAACoC,KAAK,CAACY,YAAY;AACxCC,MAAAA,aAAa,EAAEjD,OAAO,CAACoC,KAAK,CAACa,aAAa;AAC1CgK,MAAAA,gBAAgB,EACdjN,OAAO,CAACoC,KAAK,CAACa,aAAa,IAAI,IAAI,IACnCjD,OAAO,CAACoC,KAAK,CAACY,YAAY,IAAI,IAAI;AACpCkK,MAAAA,gBAAgB,EAAElN,OAAO,CAACoC,KAAK,CAAC8K,gBAAgB;AAChDC,MAAAA,MAAM,EAAEnN,OAAO,CAACoC,KAAK,CAAC+K,MAAM;AAC5BhN,MAAAA,IAAI,EAAEH,OAAO,CAACoC,KAAK,CAACjC,IAAAA;KACrB,CAAA;AAED,IAAA,IAAIH,OAAO,CAACoC,KAAK,CAACQ,QAAQ,EAAE;AAC1B3D,MAAAA,KAAK,CAAC2D,QAAQ,GAAGyI,wBAAwB,CACvCrL,OAAO,CAACoC,KAAK,CAACQ,QAAQ,EACtBgK,QAAQ,CACT,CAAA;AACF,KAAA;AAEDrO,IAAAA,MAAM,CAACd,IAAI,CAACwB,KAAK,CAAC,CAAA;AACpB,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOV,MAAM,CAAA;AACf,CAAA;AAEA;;AAEG;AACG,SAAU6O,aAAaA,CAC3BpT,OAA4B,EAAA;EAE5B,OAAOqG,cAAc,CAACrG,OAAO,CAAC,CAAA;AAChC;;ACtfA,SAASqT,kBAAkBA,CAACpO,KAAkB,EAAA;AAC5C,EAAA,IAAIqO,OAAO,GAAyD;AAClE;AACA;IACAL,gBAAgB,EAAEhO,KAAK,CAACgE,aAAa,IAAI,IAAI,IAAIhE,KAAK,CAAC+D,YAAY,IAAI,IAAA;GACxE,CAAA;EAED,IAAI/D,KAAK,CAACiB,SAAS,EAAE;AACnB,IAAa;MACX,IAAIjB,KAAK,CAACe,OAAO,EAAE;AACjB9C,QAAAC,cAAO,CACL,KAAK,EACL,wEAAwE,GACtE,2BAA2B,CAC9B,CAAA,CAAA;AACF,OAAA;AACF,KAAA;AACDqD,IAAAA,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBtN,OAAO,eAAEzG,KAAK,CAACwE,aAAa,CAACkB,KAAK,CAACiB,SAAS,CAAC;AAC7CA,MAAAA,SAAS,EAAED,SAAAA;AACZ,KAAA,CAAC,CAAA;AACH,GAAA;EAED,IAAIhB,KAAK,CAAC+E,eAAe,EAAE;AACzB,IAAa;MACX,IAAI/E,KAAK,CAACgF,sBAAsB,EAAE;AAChC/G,QAAAC,cAAO,CACL,KAAK,EACL,6FAA6F,GAC3F,iCAAiC,CACpC,CAAA,CAAA;AACF,OAAA;AACF,KAAA;AACDqD,IAAAA,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBrJ,sBAAsB,eAAE1K,KAAK,CAACwE,aAAa,CAACkB,KAAK,CAAC+E,eAAe,CAAC;AAClEA,MAAAA,eAAe,EAAE/D,SAAAA;AAClB,KAAA,CAAC,CAAA;AACH,GAAA;EAED,IAAIhB,KAAK,CAACgE,aAAa,EAAE;AACvB,IAAa;MACX,IAAIhE,KAAK,CAAC+D,YAAY,EAAE;AACtB9F,QAAAC,cAAO,CACL,KAAK,EACL,iFAAiF,GAC/E,+BAA+B,CAClC,CAAA,CAAA;AACF,OAAA;AACF,KAAA;AACDqD,IAAAA,MAAM,CAACC,MAAM,CAAC6M,OAAO,EAAE;MACrBtK,YAAY,eAAEzJ,KAAK,CAACwE,aAAa,CAACkB,KAAK,CAACgE,aAAa,CAAC;AACtDA,MAAAA,aAAa,EAAEhD,SAAAA;AAChB,KAAA,CAAC,CAAA;AACH,GAAA;AAED,EAAA,OAAOqN,OAAO,CAAA;AAChB,CAAA;AAEgB,SAAAC,kBAAkBA,CAChChP,MAAqB,EACrBqL,IAQC,EAAA;AAED,EAAA,OAAO4D,YAAY,CAAC;IAClBhT,QAAQ,EAAEoP,IAAI,EAAEpP,QAAQ;AACxB6B,IAAAA,MAAM,EAAE;MACN,GAAGuN,IAAI,EAAEvN,MAAM;AACfoR,MAAAA,kBAAkB,EAAE,IAAA;KACrB;IACDnD,OAAO,EAAEF,mBAAmB,CAAC;MAC3BH,cAAc,EAAEL,IAAI,EAAEK,cAAc;MACpCC,YAAY,EAAEN,IAAI,EAAEM,YAAAA;KACrB,CAAC;IACFwD,aAAa,EAAE9D,IAAI,EAAE8D,aAAa;IAClCnP,MAAM;IACN8O,kBAAkB;IAClBM,YAAY,EAAE/D,IAAI,EAAE+D,YAAY;IAChCC,uBAAuB,EAAEhE,IAAI,EAAEgE,uBAAAA;GAChC,CAAC,CAACC,UAAU,EAAE,CAAA;AACjB;;;;"}