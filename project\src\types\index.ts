export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'user' | 'admin';
}

export interface Chatbot {
  id: string;
  title: string;
  description: string;
  icon?: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  files: UploadedFile[];
}

export interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedAt: string;
}

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: string;
  botId: string;
}

export interface ChatSession {
  id: string;
  botId: string;
  userId: string;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
}