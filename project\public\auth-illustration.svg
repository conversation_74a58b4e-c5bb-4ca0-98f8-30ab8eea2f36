<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#1a1b4b"/>
  
  <!-- Person -->
  <g transform="translate(50, 80)">
    <!-- Body -->
    <rect x="15" y="40" width="30" height="50" rx="15" fill="#f4a261"/>
    <!-- Head -->
    <circle cx="30" cy="25" r="15" fill="#f4a261"/>
    <!-- Hair -->
    <path d="M 20 15 Q 30 10 40 15 Q 35 20 30 20 Q 25 20 20 15" fill="#8b4513"/>
    <!-- Arms -->
    <rect x="5" y="45" width="15" height="8" rx="4" fill="#f4a261"/>
    <rect x="40" y="45" width="15" height="8" rx="4" fill="#f4a261"/>
    <!-- Legs -->
    <rect x="20" y="85" width="8" height="25" rx="4" fill="#2a9d8f"/>
    <rect x="32" y="85" width="8" height="25" rx="4" fill="#2a9d8f"/>
  </g>
  
  <!-- Clipboard -->
  <g transform="translate(120, 40)">
    <!-- Clipboard base -->
    <rect x="0" y="10" width="80" height="100" rx="5" fill="#264653"/>
    <!-- Clipboard top -->
    <rect x="25" y="0" width="30" height="15" rx="7" fill="#e9c46a"/>
    <!-- Paper -->
    <rect x="8" y="20" width="64" height="80" fill="white"/>
    
    <!-- Checklist items -->
    <g fill="#2a9d8f">
      <!-- Check 1 -->
      <rect x="15" y="30" width="8" height="8" fill="#2a9d8f"/>
      <path d="M 17 32 L 19 34 L 21 30" stroke="white" stroke-width="1.5" fill="none"/>
      <rect x="28" y="30" width="35" height="2" fill="#264653"/>
      <rect x="28" y="34" width="25" height="2" fill="#264653"/>
      
      <!-- Check 2 -->
      <rect x="15" y="45" width="8" height="8" fill="#2a9d8f"/>
      <path d="M 17 47 L 19 49 L 21 45" stroke="white" stroke-width="1.5" fill="none"/>
      <rect x="28" y="45" width="35" height="2" fill="#264653"/>
      <rect x="28" y="49" width="25" height="2" fill="#264653"/>
      
      <!-- Check 3 -->
      <rect x="15" y="60" width="8" height="8" fill="#2a9d8f"/>
      <path d="M 17 62 L 19 64 L 21 60" stroke="white" stroke-width="1.5" fill="none"/>
      <rect x="28" y="60" width="35" height="2" fill="#264653"/>
      <rect x="28" y="64" width="25" height="2" fill="#264653"/>
      
      <!-- Check 4 -->
      <rect x="15" y="75" width="8" height="8" fill="#2a9d8f"/>
      <path d="M 17 77 L 19 79 L 21 75" stroke="white" stroke-width="1.5" fill="none"/>
      <rect x="28" y="75" width="35" height="2" fill="#264653"/>
      <rect x="28" y="79" width="25" height="2" fill="#264653"/>
    </g>
  </g>
  
  <!-- Documents/Papers -->
  <g transform="translate(220, 60)">
    <!-- Paper stack -->
    <rect x="0" y="20" width="60" height="80" fill="white" opacity="0.9"/>
    <rect x="5" y="15" width="60" height="80" fill="white" opacity="0.7"/>
    <rect x="10" y="10" width="60" height="80" fill="white"/>
    
    <!-- Lines on top paper -->
    <g stroke="#264653" stroke-width="1" opacity="0.3">
      <line x1="15" y1="20" x2="60" y2="20"/>
      <line x1="15" y1="25" x2="55" y2="25"/>
      <line x1="15" y1="30" x2="60" y2="30"/>
      <line x1="15" y1="35" x2="50" y2="35"/>
      <line x1="15" y1="40" x2="60" y2="40"/>
    </g>
  </g>
  
  <!-- Boxes/Packages -->
  <g transform="translate(290, 120)">
    <!-- Large box -->
    <rect x="0" y="20" width="40" height="40" fill="#e9c46a"/>
    <rect x="0" y="20" width="40" height="8" fill="#f4a261"/>
    <!-- Box handle -->
    <rect x="15" y="15" width="10" height="8" rx="2" fill="#e9c46a"/>
    
    <!-- Medium box -->
    <rect x="45" y="30" width="30" height="30" fill="#e9c46a"/>
    <rect x="45" y="30" width="30" height="6" fill="#f4a261"/>
    <!-- Box handle -->
    <rect x="55" y="25" width="8" height="6" rx="2" fill="#e9c46a"/>
    
    <!-- Small box -->
    <rect x="20" y="65" width="25" height="25" fill="#e9c46a"/>
    <rect x="20" y="65" width="25" height="5" fill="#f4a261"/>
    <!-- Box handle -->
    <rect x="28" y="62" width="6" height="5" rx="1" fill="#e9c46a"/>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="350" cy="50" r="3" fill="#e9c46a" opacity="0.6"/>
  <circle cx="370" cy="80" r="2" fill="#f4a261" opacity="0.6"/>
  <circle cx="30" cy="250" r="2" fill="#2a9d8f" opacity="0.6"/>
  <circle cx="60" cy="270" r="3" fill="#e9c46a" opacity="0.6"/>
</svg>
