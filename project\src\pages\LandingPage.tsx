import React from 'react';
import { Link } from 'react-router-dom';
import {
  Bot,
  FileText,
  Search,
  MessageCircle,
  Brain,
  Target,
  Bookmark,
  Globe,
  Mic,
  Shield,
  Download
} from 'lucide-react';
import { motion } from 'framer-motion';

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-navy-900 relative overflow-hidden">
      {/* Header with logo */}
      <header className="relative z-20 p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-3">
            <img
              src="/image.png"
              alt="DocuChat Logo"
              className="w-10 h-10 md:w-12 md:h-12"
            />
            <span className="text-xl md:text-2xl font-bold text-white">DocuChat</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Link
              to="/login"
              className="text-white hover:text-gray-300 px-3 py-2 rounded-md text-sm font-medium"
            >
              Sign in
            </Link>
            <Link
              to="/register"
              className="bg-pink-500 text-white hover:bg-pink-600 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Sign up
            </Link>
          </div>
        </div>
      </header>

      {/* Animated particles */}
      <div className="absolute inset-0">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full bg-yellow-400/50"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center">
        {/* Robot mascot */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="w-32 h-32 md:w-40 md:h-40 bg-gradient-to-br from-blue-600 to-blue-800 rounded-full flex items-center justify-center shadow-lg">
            <div className="relative">
              {/* Robot head */}
              <div className="w-16 h-12 bg-white rounded-lg relative">
                {/* Eyes */}
                <div className="absolute top-2 left-2 w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="absolute top-2 right-2 w-3 h-3 bg-blue-500 rounded-full"></div>
                {/* Mouth */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-1 bg-gray-400 rounded"></div>
              </div>
              {/* Body indicator */}
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
        </motion.div>

        <motion.h1
          className="text-4xl md:text-6xl font-bold text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Create and train a
        </motion.h1>

        <motion.h1
          className="text-5xl md:text-7xl font-bold text-pink-500 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Chatbot
        </motion.h1>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Link
            to="/login"
            className="inline-block px-8 py-3 text-lg font-medium text-navy-900 bg-white rounded-md hover:bg-gray-100 transition-colors duration-200 transform hover:scale-105"
          >
            Let's Start
          </Link>
        </motion.div>
      </div>

      {/* Features Section */}
      <div className="relative z-10 py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              🌟 Suggested Features for Your Docu Chatbot
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Powerful features to enhance your document interaction experience
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <div className="flex items-center mb-4">
                <FileText className="h-8 w-8 text-pink-400 mr-3" />
                <h3 className="text-xl font-bold text-white">📄 Multi-PDF Upload Support</h3>
              </div>
              <p className="text-gray-300">
                Let users upload and chat with multiple documents at once.
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
            >
              <div className="flex items-center mb-4">
                <Search className="h-8 w-8 text-blue-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🔍 Smart Document Search</h3>
              </div>
              <p className="text-gray-300">
                Add a search bar to instantly find keywords or phrases within the document.
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <div className="flex items-center mb-4">
                <MessageCircle className="h-8 w-8 text-green-400 mr-3" />
                <h3 className="text-xl font-bold text-white">💬 Context-Aware Chat History</h3>
              </div>
              <p className="text-gray-300">
                Keep a thread of the conversation so the chatbot remembers context across multiple questions.
              </p>
            </motion.div>

            {/* Feature 4 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.1 }}
            >
              <div className="flex items-center mb-4">
                <Brain className="h-8 w-8 text-purple-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🧠 Summarization Mode</h3>
              </div>
              <p className="text-gray-300">
                One-click summary of the whole document or selected sections.
              </p>
            </motion.div>

            {/* Feature 5 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
            >
              <div className="flex items-center mb-4">
                <Target className="h-8 w-8 text-yellow-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🎯 Ask Specific Sections</h3>
              </div>
              <p className="text-gray-300">
                Let users select or highlight parts of the document and ask questions directly about them.
              </p>
            </motion.div>

            {/* Feature 6 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.3 }}
            >
              <div className="flex items-center mb-4">
                <Bookmark className="h-8 w-8 text-orange-400 mr-3" />
                <h3 className="text-xl font-bold text-white">📌 Save Important Answers</h3>
              </div>
              <p className="text-gray-300">
                Allow users to "pin" or bookmark answers for future reference.
              </p>
            </motion.div>

            {/* Feature 7 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
            >
              <div className="flex items-center mb-4">
                <Globe className="h-8 w-8 text-cyan-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🌐 Language Translation</h3>
              </div>
              <p className="text-gray-300">
                Enable support to translate document content and chatbot replies into multiple languages.
              </p>
            </motion.div>

            {/* Feature 8 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.5 }}
            >
              <div className="flex items-center mb-4">
                <Mic className="h-8 w-8 text-red-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🎤 Voice Input</h3>
              </div>
              <p className="text-gray-300">
                Let users ask questions by speaking instead of typing.
              </p>
            </motion.div>

            {/* Feature 9 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.6 }}
            >
              <div className="flex items-center mb-4">
                <Shield className="h-8 w-8 text-indigo-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🔒 Secure Upload & Access</h3>
              </div>
              <p className="text-gray-300">
                Make sure documents are securely stored and processed — add user authentication if needed.
              </p>
            </motion.div>

            {/* Feature 10 */}
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 md:col-span-2 lg:col-span-1"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.7 }}
            >
              <div className="flex items-center mb-4">
                <Download className="h-8 w-8 text-emerald-400 mr-3" />
                <h3 className="text-xl font-bold text-white">🧾 Export Chat History as PDF</h3>
              </div>
              <p className="text-gray-300">
                Allow users to export their conversation with the chatbot as a well-formatted PDF.
              </p>
            </motion.div>
          </div>

          {/* Call to Action */}
          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.8 }}
          >
            <Link
              to="/register"
              className="inline-block px-8 py-4 text-lg font-medium text-white bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Start Building Your Chatbot Today
            </Link>
          </motion.div>
        </div>
      </div>

      {/* Animated waves */}
      <div className="absolute bottom-0 left-0 right-0">
        <motion.div
          className="relative"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto opacity-20">
            <path
              fill="currentColor"
              className="text-indigo-500"
              d="M0,288L48,272C96,256,192,224,288,213.3C384,203,480,213,576,213.3C672,213,768,203,864,165.3C960,128,1056,64,1152,58.7C1248,53,1344,107,1392,133.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
            ></path>
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto absolute bottom-0 opacity-20">
            <path
              fill="currentColor"
              className="text-purple-500"
              d="M0,96L48,128C96,160,192,224,288,240C384,256,480,224,576,197.3C672,171,768,149,864,165.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
            ></path>
          </svg>
        </motion.div>
      </div>
    </div>
  );
};

export default LandingPage;