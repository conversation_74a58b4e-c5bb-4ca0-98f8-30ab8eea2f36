/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        navy: {
          900: '#0a0a29',
          800: '#12123e',
          700: '#1a1a52',
          600: '#2b2b73',
          500: '#3d3d94',
          400: '#5858b5',
          300: '#8181d1',
          200: '#b0b0e6',
          100: '#d8d8f5',
          50: '#f0f0ff',
        },
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
    },
  },
  plugins: [],
};