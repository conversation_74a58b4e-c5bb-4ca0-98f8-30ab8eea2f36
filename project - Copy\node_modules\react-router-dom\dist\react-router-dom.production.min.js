/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import*as e from"react";import*as t from"react-dom";import{UNSAFE_mapRouteProperties as n,UNSAFE_logV6DeprecationWarnings as r,UNSAFE_DataRouterContext as a,UNSAFE_DataRouterStateContext as o,Router as i,UNSAFE_useRoutesImpl as s,UNSAFE_NavigationContext as u,useHref as c,useResolvedPath as l,useLocation as f,useNavigate as d,createPath as m,UNSAFE_useRouteId as h,UNSAFE_RouteContext as p,useMatches as w,useNavigation as v,useBlocker as g}from"react-router";export{AbortedDeferredError,Await,MemoryRouter,Navigate,NavigationType,Outlet,Route,Router,Routes,UNSAFE_DataRouterContext,UNSAFE_DataRouterStateContext,UNSAFE_LocationContext,UNSAFE_NavigationContext,UNSAFE_RouteContext,UNSAFE_useRouteId,createMemoryRouter,createPath,createRoutesFromChildren,createRoutesFromElements,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,redirectDocument,renderMatches,replace,resolvePath,useActionData,useAsyncError,useAsyncValue,useBlocker,useHref,useInRouterContext,useLoaderData,useLocation,useMatch,useMatches,useNavigate,useNavigation,useNavigationType,useOutlet,useOutletContext,useParams,useResolvedPath,useRevalidator,useRouteError,useRouteLoaderData,useRoutes}from"react-router";import{stripBasename as y,createRouter as b,createBrowserHistory as S,createHashHistory as E,UNSAFE_ErrorResponseImpl as R,UNSAFE_invariant as T,joinPaths as L,IDLE_FETCHER as x,matchPath as C}from"@remix-run/router";export{UNSAFE_ErrorResponseImpl}from"@remix-run/router";const _="application/x-www-form-urlencoded";function A(e){return null!=e&&"string"==typeof e.tagName}function F(e=""){return new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map((e=>[n,e])):[[n,r]])}),[]))}let U=null;const k=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function N(e){return null==e||k.has(e)?e:null}function P(e,t){let n,r,a,o,i;if(A(s=e)&&"form"===s.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?y(i,t):null,n=e.getAttribute("method")||"get",a=N(e.getAttribute("enctype"))||_,o=new FormData(e)}else if(function(e){return A(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return A(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(r=s?y(s,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||"get",a=N(e.getAttribute("formenctype"))||N(i.getAttribute("enctype"))||_,o=new FormData(i,e),!function(){if(null===U)try{new FormData(document.createElement("form"),0),U=!1}catch(e){U=!0}return U}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(A(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n="get",r=null,a=_,i=e}var s;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}try{window.__reactRouterVersion="6"}catch(Se){}function D(e,t){return b({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:S({window:t?.window}),hydrationData:t?.hydrationData||K(),routes:e,mapRouteProperties:n,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function O(e,t){return b({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:E({window:t?.window}),hydrationData:t?.hydrationData||K(),routes:e,mapRouteProperties:n,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function K(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:M(e.errors)}),e}function M(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,a]of t)if(a&&"RouteErrorResponse"===a.__type)n[r]=new R(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let e=window[a.__subType];if("function"==typeof e)try{let t=new e(a.message);t.stack="",n[r]=t}catch(Se){}}if(null==n[r]){let e=new Error(a.message);e.stack="",n[r]=e}}else n[r]=a;return n}const j=e.createContext({isTransitioning:!1}),I=e.createContext(new Map),V=e.startTransition,H=t.flushSync,z=e.useId;function B(e){H?H(e):e()}class ${status="pending";constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}}))}}function W({fallbackElement:t,router:n,future:s}){let[u,c]=e.useState(n.state),[l,f]=e.useState(),[d,m]=e.useState({isTransitioning:!1}),[h,p]=e.useState(),[w,v]=e.useState(),[g,y]=e.useState(),b=e.useRef(new Map),{v7_startTransition:S}=s||{},E=e.useCallback((e=>{S?function(e){V?V(e):e()}(e):e()}),[S]),R=e.useCallback(((e,{deletedFetchers:t,flushSync:r,viewTransitionOpts:a})=>{e.fetchers.forEach(((e,t)=>{void 0!==e.data&&b.current.set(t,e.data)})),t.forEach((e=>b.current.delete(e)));let o=null==n.window||null==n.window.document||"function"!=typeof n.window.document.startViewTransition;if(a&&!o){if(r){B((()=>{w&&(h&&h.resolve(),w.skipTransition()),m({isTransitioning:!0,flushSync:!0,currentLocation:a.currentLocation,nextLocation:a.nextLocation})}));let t=n.window.document.startViewTransition((()=>{B((()=>c(e)))}));return t.finished.finally((()=>{B((()=>{p(void 0),v(void 0),f(void 0),m({isTransitioning:!1})}))})),void B((()=>v(t)))}w?(h&&h.resolve(),w.skipTransition(),y({state:e,currentLocation:a.currentLocation,nextLocation:a.nextLocation})):(f(e),m({isTransitioning:!0,flushSync:!1,currentLocation:a.currentLocation,nextLocation:a.nextLocation}))}else r?B((()=>c(e))):E((()=>c(e)))}),[n.window,w,h,b,E]);e.useLayoutEffect((()=>n.subscribe(R)),[n,R]),e.useEffect((()=>{d.isTransitioning&&!d.flushSync&&p(new $)}),[d]),e.useEffect((()=>{if(h&&l&&n.window){let e=l,t=h.promise,r=n.window.document.startViewTransition((async()=>{E((()=>c(e))),await t}));r.finished.finally((()=>{p(void 0),v(void 0),f(void 0),m({isTransitioning:!1})})),v(r)}}),[E,l,h,n.window]),e.useEffect((()=>{h&&l&&u.location.key===l.location.key&&h.resolve()}),[h,w,u.location,l]),e.useEffect((()=>{!d.isTransitioning&&g&&(f(g.state),m({isTransitioning:!0,flushSync:!1,currentLocation:g.currentLocation,nextLocation:g.nextLocation}),y(void 0))}),[d.isTransitioning,g]),e.useEffect((()=>{}),[]);let T=e.useMemo((()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:e=>n.navigate(e),push:(e,t,r)=>n.navigate(e,{state:t,preventScrollReset:r?.preventScrollReset}),replace:(e,t,r)=>n.navigate(e,{replace:!0,state:t,preventScrollReset:r?.preventScrollReset})})),[n]),L=n.basename||"/",x=e.useMemo((()=>({router:n,navigator:T,static:!1,basename:L})),[n,T,L]),C=e.useMemo((()=>({v7_relativeSplatPath:n.future.v7_relativeSplatPath})),[n.future.v7_relativeSplatPath]);return e.useEffect((()=>r(s,n.future)),[s,n.future]),e.createElement(e.Fragment,null,e.createElement(a.Provider,{value:x},e.createElement(o.Provider,{value:u},e.createElement(I.Provider,{value:b.current},e.createElement(j.Provider,{value:d},e.createElement(i,{basename:L,location:u.location,navigationType:u.historyAction,navigator:T,future:C},u.initialized||n.future.v7_partialHydration?e.createElement(Y,{routes:n.routes,future:n.future,state:u}):t))))),null)}const Y=e.memo(J);function J({routes:e,future:t,state:n}){return s(e,void 0,n,t)}function q({basename:t,children:n,future:a,window:o}){let s=e.useRef();null==s.current&&(s.current=S({window:o,v5Compat:!0}));let u=s.current,[c,l]=e.useState({action:u.action,location:u.location}),{v7_startTransition:f}=a||{},d=e.useCallback((e=>{f&&V?V((()=>l(e))):l(e)}),[l,f]);return e.useLayoutEffect((()=>u.listen(d)),[u,d]),e.useEffect((()=>r(a)),[a]),e.createElement(i,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:u,future:a})}function G({basename:t,children:n,future:a,window:o}){let s=e.useRef();null==s.current&&(s.current=E({window:o,v5Compat:!0}));let u=s.current,[c,l]=e.useState({action:u.action,location:u.location}),{v7_startTransition:f}=a||{},d=e.useCallback((e=>{f&&V?V((()=>l(e))):l(e)}),[l,f]);return e.useLayoutEffect((()=>u.listen(d)),[u,d]),e.useEffect((()=>r(a)),[a]),e.createElement(i,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:u,future:a})}function Q({basename:t,children:n,future:a,history:o}){let[s,u]=e.useState({action:o.action,location:o.location}),{v7_startTransition:c}=a||{},l=e.useCallback((e=>{c&&V?V((()=>u(e))):u(e)}),[u,c]);return e.useLayoutEffect((()=>o.listen(l)),[o,l]),e.useEffect((()=>r(a)),[a]),e.createElement(i,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:o,future:a})}const X="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Z=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ee=e.forwardRef((function({onClick:t,relative:n,reloadDocument:r,replace:a,state:o,target:i,to:s,preventScrollReset:l,viewTransition:f,...d},m){let h,{basename:p}=e.useContext(u),w=!1;if("string"==typeof s&&Z.test(s)&&(h=s,X))try{let e=new URL(window.location.href),t=s.startsWith("//")?new URL(e.protocol+s):new URL(s),n=y(t.pathname,p);t.origin===e.origin&&null!=n?s=n+t.search+t.hash:w=!0}catch(Se){}let v=c(s,{relative:n}),g=ue(s,{replace:a,state:o,target:i,preventScrollReset:l,relative:n,viewTransition:f});return e.createElement("a",Object.assign({},d,{href:h||v,onClick:w||r?t:function(e){t&&t(e),e.defaultPrevented||g(e)},ref:m,target:i}))})),te=e.forwardRef((function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:a=!1,style:i,to:s,viewTransition:c,children:d,...m},h){let p=l(s,{relative:m.relative}),w=f(),v=e.useContext(o),{navigator:g,basename:b}=e.useContext(u),S=null!=v&&be(p)&&!0===c,E=g.encodeLocation?g.encodeLocation(p).pathname:p.pathname,R=w.pathname,T=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;n||(R=R.toLowerCase(),T=T?T.toLowerCase():null,E=E.toLowerCase()),T&&b&&(T=y(T,b)||T);const L="/"!==E&&E.endsWith("/")?E.length-1:E.length;let x,C=R===E||!a&&R.startsWith(E)&&"/"===R.charAt(L),_=null!=T&&(T===E||!a&&T.startsWith(E)&&"/"===T.charAt(E.length)),A={isActive:C,isPending:_,isTransitioning:S},F=C?t:void 0;x="function"==typeof r?r(A):[r,C?"active":null,_?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let U="function"==typeof i?i(A):i;return e.createElement(ee,Object.assign({},m,{"aria-current":F,className:x,ref:h,style:U,to:s,viewTransition:c}),"function"==typeof d?d(A):d)})),ne=e.forwardRef((({fetcherKey:t,navigate:n,reloadDocument:r,replace:a,state:o,method:i="get",action:s,onSubmit:u,relative:c,preventScrollReset:l,viewTransition:f,...d},m)=>{let h=de(),p=me(s,{relative:c}),w="get"===i.toLowerCase()?"get":"post";return e.createElement("form",Object.assign({ref:m,method:w,action:p,onSubmit:r?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,s=r?.getAttribute("formmethod")||i;h(r||e.currentTarget,{fetcherKey:t,method:s,navigate:n,replace:a,state:o,relative:c,preventScrollReset:l,viewTransition:f})}},d))}));function re({getKey:e,storageKey:t}){return ve({getKey:e,storageKey:t}),null}var ae=function(e){return e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState",e}(ae||{}),oe=function(e){return e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration",e}(oe||{});function ie(t){let n=e.useContext(a);return n||T(!1),n}function se(t){let n=e.useContext(o);return n||T(!1),n}function ue(t,{target:n,replace:r,state:a,preventScrollReset:o,relative:i,viewTransition:s}={}){let u=d(),c=f(),h=l(t,{relative:i});return e.useCallback((e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,n)){e.preventDefault();let n=void 0!==r?r:m(c)===m(h);u(t,{replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:s})}}),[c,u,h,r,a,n,t,o,i,s])}function ce(t){let n=e.useRef(F(t)),r=e.useRef(!1),a=f(),o=e.useMemo((()=>function(e,t){let n=F(e);return t&&t.forEach(((e,r)=>{n.has(r)||t.getAll(r).forEach((e=>{n.append(r,e)}))})),n}(a.search,r.current?null:n.current)),[a.search]),i=d(),s=e.useCallback(((e,t)=>{const n=F("function"==typeof e?e(o):e);r.current=!0,i("?"+n,t)}),[i,o]);return[o,s]}let le=0,fe=()=>`__${String(++le)}__`;function de(){let{router:t}=ie(ae.UseSubmit),{basename:n}=e.useContext(u),r=h();return e.useCallback(((e,a={})=>{!function(){if("undefined"==typeof document)throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}();let{action:o,method:i,encType:s,formData:u,body:c}=P(e,n);if(!1===a.navigate){let e=a.fetcherKey||fe();t.fetch(e,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:c,formMethod:a.method||i,formEncType:a.encType||s,flushSync:a.flushSync})}else t.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:c,formMethod:a.method||i,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})}),[t,n,r])}function me(t,{relative:n}={}){let{basename:r}=e.useContext(u),a=e.useContext(p);a||T(!1);let[o]=a.matches.slice(-1),i={...l(t||".",{relative:n})},s=f();if(null==t){i.search=s.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();i.search=n?`?${n}`:""}}return t&&"."!==t||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),"/"!==r&&(i.pathname="/"===i.pathname?r:L([r,i.pathname])),m(i)}function he({key:t}={}){let{router:n}=ie(ae.UseFetcher),r=se(oe.UseFetcher),a=e.useContext(I),o=e.useContext(p),i=o.matches[o.matches.length-1]?.route.id;a||T(!1),o||T(!1),null==i&&T(!1);let s=z?z():"",[u,c]=e.useState(t||s);t&&t!==u?c(t):u||c(fe()),e.useEffect((()=>(n.getFetcher(u),()=>{n.deleteFetcher(u)})),[n,u]);let l=e.useCallback(((e,t)=>{i||T(!1),n.fetch(u,i,e,t)}),[u,i,n]),f=de(),d=e.useCallback(((e,t)=>{f(e,{...t,navigate:!1,fetcherKey:u})}),[u,f]),m=e.useMemo((()=>e.forwardRef(((t,n)=>e.createElement(ne,Object.assign({},t,{navigate:!1,fetcherKey:u,ref:n}))))),[u]),h=r.fetchers.get(u)||x,w=a.get(u);return e.useMemo((()=>({Form:m,submit:d,load:l,...h,data:w})),[m,d,l,h,w])}function pe(){let e=se(oe.UseFetchers);return Array.from(e.fetchers.entries()).map((([e,t])=>({...t,key:e})))}let we={};function ve({getKey:t,storageKey:n}={}){let{router:r}=ie(ae.UseScrollRestoration),{restoreScrollPosition:a,preventScrollReset:o}=se(oe.UseScrollRestoration),{basename:i}=e.useContext(u),s=f(),c=w(),l=v();e.useEffect((()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"})),[]),function(t,n){let{capture:r}=n||{};e.useEffect((()=>{let e=null!=r?{capture:r}:void 0;return window.addEventListener("pagehide",t,e),()=>{window.removeEventListener("pagehide",t,e)}}),[t,r])}(e.useCallback((()=>{if("idle"===l.state){let e=(t?t(s,c):null)||s.key;we[e]=window.scrollY}try{sessionStorage.setItem(n||"react-router-scroll-positions",JSON.stringify(we))}catch(e){}window.history.scrollRestoration="auto"}),[n,t,l.state,s,c])),"undefined"!=typeof document&&(e.useLayoutEffect((()=>{try{let e=sessionStorage.getItem(n||"react-router-scroll-positions");e&&(we=JSON.parse(e))}catch(Se){}}),[n]),e.useLayoutEffect((()=>{let e=t&&"/"!==i?(e,n)=>t({...e,pathname:y(e.pathname,i)||e.pathname},n):t,n=r?.enableScrollRestoration(we,(()=>window.scrollY),e);return()=>n&&n()}),[r,i,t]),e.useLayoutEffect((()=>{if(!1!==a)if("number"!=typeof a){if(s.hash){let e=document.getElementById(decodeURIComponent(s.hash.slice(1)));if(e)return void e.scrollIntoView()}!0!==o&&window.scrollTo(0,0)}else window.scrollTo(0,a)}),[s,a,o]))}function ge(t,n){let{capture:r}=n||{};e.useEffect((()=>{let e=null!=r?{capture:r}:void 0;return window.addEventListener("beforeunload",t,e),()=>{window.removeEventListener("beforeunload",t,e)}}),[t,r])}function ye({when:t,message:n}){let r=g(t);e.useEffect((()=>{if("blocked"===r.state){window.confirm(n)?setTimeout(r.proceed,0):r.reset()}}),[r,n]),e.useEffect((()=>{"blocked"!==r.state||t||r.reset()}),[r,t])}function be(t,n={}){let r=e.useContext(j);null==r&&T(!1);let{basename:a}=ie(ae.useViewTransitionState),o=l(t,{relative:n.relative});if(!r.isTransitioning)return!1;let i=y(r.currentLocation.pathname,a)||r.currentLocation.pathname,s=y(r.nextLocation.pathname,a)||r.nextLocation.pathname;return null!=C(o.pathname,s)||null!=C(o.pathname,i)}export{q as BrowserRouter,ne as Form,G as HashRouter,ee as Link,te as NavLink,W as RouterProvider,re as ScrollRestoration,I as UNSAFE_FetchersContext,j as UNSAFE_ViewTransitionContext,ve as UNSAFE_useScrollRestoration,D as createBrowserRouter,O as createHashRouter,F as createSearchParams,Q as unstable_HistoryRouter,ye as unstable_usePrompt,ge as useBeforeUnload,he as useFetcher,pe as useFetchers,me as useFormAction,ue as useLinkClickHandler,ce as useSearchParams,de as useSubmit,be as useViewTransitionState};
//# sourceMappingURL=react-router-dom.production.min.js.map
