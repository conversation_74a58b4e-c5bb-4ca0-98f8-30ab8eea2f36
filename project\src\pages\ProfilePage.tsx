import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Save, User, Mail, Calendar, Shield } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import Navbar from '../components/layout/Navbar';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import AvatarUpload from '../components/ui/AvatarUpload';

const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

const ProfilePage: React.FC = () => {
  const { user, updateProfile, isLoading } = useAuthStore();
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { register, handleSubmit, formState: { errors } } = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user?.name || '',
    },
  });

  const handleAvatarChange = (file: File | null, previewUrl: string | null) => {
    setAvatarFile(file);
    setAvatarPreview(previewUrl);
  };

  const onSubmit = async (data: ProfileFormValues) => {
    try {
      setError(null);
      setSuccess(null);

      const updates: { name: string; avatar?: string } = {
        name: data.name,
      };

      // If there's a new avatar file, convert it to base64 for storage
      if (avatarFile) {
        const reader = new FileReader();
        reader.onload = async () => {
          updates.avatar = reader.result as string;
          await updateProfile(updates);
          setSuccess('Profile updated successfully!');
          setAvatarFile(null);
          setAvatarPreview(null);
        };
        reader.readAsDataURL(avatarFile);
      } else {
        // If avatar was removed
        if (avatarPreview === null && user?.avatar) {
          updates.avatar = undefined;
        }
        await updateProfile(updates);
        setSuccess('Profile updated successfully!');
      }
    } catch (error) {
      console.error('Profile update failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to update profile. Please try again.');
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-navy-900 flex flex-col">
      <Navbar />

      <main className="flex-1">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-8">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {(avatarPreview || user.avatar) ? (
                      <img
                        src={avatarPreview || user.avatar}
                        alt={user.name}
                        className="h-16 w-16 rounded-full object-cover border-4 border-white/20"
                      />
                    ) : (
                      <div className="h-16 w-16 rounded-full bg-white/20 flex items-center justify-center border-4 border-white/20">
                        <span className="text-white text-2xl font-bold">
                          {user.name?.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold text-white">Welcome, {user.name}!</h1>
                    <p className="text-indigo-100 mt-2">
                      {user.avatar ? 'Manage your account information and preferences' : 'Upload your profile picture to personalize your account'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
                  {/* Avatar Section */}
                  <div className="flex flex-col md:flex-row md:items-start md:space-x-8">
                    <div className="flex-shrink-0 mb-6 md:mb-0">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Profile Picture</h3>
                      <AvatarUpload
                        currentAvatar={avatarPreview || user.avatar}
                        onAvatarChange={handleAvatarChange}
                        size="lg"
                        disabled={isLoading}
                      />
                      <div className="text-center mt-4">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{user.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {user.avatar ? user.email : 'Upload your first profile picture'}
                        </p>
                      </div>
                      {!user.avatar && !avatarPreview && (
                        <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                          <p className="text-xs text-blue-600 dark:text-blue-400 text-center">
                            👆 Click above to upload your profile picture
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Profile Information */}
                    <div className="flex-1 space-y-6">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">Personal Information</h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <User className="h-4 w-4 inline mr-2" />
                            Full Name
                          </label>
                          <Input
                            id="name"
                            type="text"
                            placeholder="Enter your full name"
                            {...register('name')}
                            error={errors.name?.message}
                            disabled={isLoading}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <Mail className="h-4 w-4 inline mr-2" />
                            Email Address
                          </label>
                          <Input
                            type="email"
                            value={user.email}
                            disabled
                            className="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Email cannot be changed
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <Shield className="h-4 w-4 inline mr-2" />
                            Role
                          </label>
                          <Input
                            type="text"
                            value={user.role === 'admin' ? 'Administrator' : 'User'}
                            disabled
                            className="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <Calendar className="h-4 w-4 inline mr-2" />
                            User ID
                          </label>
                          <Input
                            type="text"
                            value={user.id}
                            disabled
                            className="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 font-mono text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  {success && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md"
                    >
                      {success}
                    </motion.div>
                  )}

                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md"
                    >
                      {error}
                    </motion.div>
                  )}

                  {/* Save Button */}
                  <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                    <Button
                      type="submit"
                      variant="primary"
                      isLoading={isLoading}
                      className="bg-indigo-600 hover:bg-indigo-700"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </motion.div>
        </div>
      </main>
    </div>
  );
};

export default ProfilePage;
