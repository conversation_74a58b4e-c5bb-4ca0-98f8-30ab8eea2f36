import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Camera, Upload, X, User } from 'lucide-react';
import { cn } from '../../lib/utils';

interface AvatarUploadProps {
  currentAvatar?: string;
  onAvatarChange: (file: File | null, previewUrl: string | null) => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatar,
  onAvatarChange,
  size = 'lg',
  className,
  disabled = false,
}) => {
  const [preview, setPreview] = useState<string | null>(currentAvatar || null);
  const [isDragActive, setIsDragActive] = useState(false);

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  };

  const iconSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);
      onAvatarChange(file, previewUrl);
    }
    setIsDragActive(false);
  }, [onAvatarChange]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024, // 5MB
    disabled,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
  });

  const removeAvatar = () => {
    setPreview(null);
    onAvatarChange(null, null);
  };

  return (
    <div className={cn('flex flex-col items-center space-y-4', className)}>
      <div className="relative">
        <div
          {...getRootProps()}
          className={cn(
            'relative rounded-full border-2 border-dashed cursor-pointer transition-all duration-200 overflow-hidden',
            sizeClasses[size],
            isDragActive 
              ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-indigo-400 dark:hover:border-indigo-500',
            disabled && 'opacity-50 cursor-not-allowed',
            preview && 'border-solid border-gray-200 dark:border-gray-700'
          )}
        >
          <input {...getInputProps()} />
          
          {preview ? (
            <img
              src={preview}
              alt="Avatar preview"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-800">
              {isDragActive ? (
                <Upload className={cn(iconSizes[size], 'text-indigo-500')} />
              ) : (
                <User className={cn(iconSizes[size], 'text-gray-400 dark:text-gray-500')} />
              )}
              {size === 'lg' && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center px-2">
                  {isDragActive ? 'Drop image' : 'Click or drag'}
                </p>
              )}
            </div>
          )}
          
          {/* Camera overlay on hover */}
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-full">
            <Camera className="h-6 w-6 text-white" />
          </div>
        </div>

        {/* Remove button */}
        {preview && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              removeAvatar();
            }}
            className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 transition-colors duration-200"
            disabled={disabled}
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>

      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Click to upload or drag and drop
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-500">
          PNG, JPG, GIF up to 5MB
        </p>
      </div>
    </div>
  );
};

export default AvatarUpload;
